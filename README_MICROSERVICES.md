# User-DF 微服务架构重构

## 概述

本项目将原有的 `orc_mongodb_service` 单体架构成功重构为异步微服务架构，实现了更好的可扩展性、可维护性和性能。

## 🎯 重构目标

- ✅ 将ORC数据处理和MongoDB数据写入分别部署为独立的异步微服务
- ✅ 支持较高的处理速度和并发能力
- ✅ 将监控程序单独作为一个脚本，提供统一的监控面板
- ✅ 完全移除多省使用tmux并行处理的逻辑

## 🏗️ 架构设计

### 微服务组件

1. **ORC数据处理微服务** (`orc_processor_service`)
   - 端口: 8001
   - 职责: ORC文件读取、数据清洗、PID去重、Milvus查询
   - 输出: 通过Redis队列发送处理结果

2. **MongoDB写入微服务** (`mongodb_writer_service`)
   - 端口: 8002
   - 职责: 从队列接收数据、批量写入MongoDB、错误处理

3. **监控服务** (`monitoring_service`)
   - 职责: 监控所有微服务状态、性能统计、实时监控面板

### 通信机制

- **消息队列**: Redis (`mongodb_write_queue`)
- **HTTP API**: 健康检查和统计接口
- **异步处理**: 支持高并发和非阻塞操作

## 📁 项目结构

```
services/
└── orc_mongodb_service/            # ORC MongoDB服务（包含微服务）
    ├── orc_processor_service/      # ORC数据处理微服务
    ├── mongodb_writer_service/     # MongoDB写入微服务
    └── monitoring_service/         # 监控服务

configs/
└── orc_mongodb_service/            # ORC MongoDB服务配置
    ├── orc_processor_service/      # ORC处理服务配置
    ├── mongodb_writer_service/     # MongoDB写入服务配置
    └── monitoring_service/         # 监控服务配置

scripts/microservices/              # 部署和管理脚本
├── start_all_services.sh          # 启动所有服务
├── stop_all_services.sh           # 停止所有服务
├── status_services.sh             # 检查服务状态
├── test_microservices.py          # 集成测试
└── demo_microservices.py          # 演示脚本

docs/
└── MICROSERVICES_ARCHITECTURE.md  # 详细架构文档
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 确保Redis服务运行
redis-server

# 确保MongoDB服务运行
mongod

# 安装Python依赖
pip install -r requirements.txt
```

### 2. 启动微服务

```bash
# 启动所有微服务（开发环境）
./scripts/microservices/start_all_services.sh

# 启动所有微服务（生产环境）
./scripts/microservices/start_all_services.sh --environment production
```

### 3. 检查服务状态

```bash
# 检查所有服务状态
./scripts/microservices/status_services.sh

# 或通过HTTP API检查
curl http://localhost:8001/health  # ORC处理服务
curl http://localhost:8002/health  # MongoDB写入服务
```

### 4. 启动监控

```bash
# 启动实时监控面板
python3 -m services.monitoring_service.monitor --mode live

# 单次状态检查
python3 -m services.monitoring_service.monitor --mode once
```

### 5. 运行演示

```bash
# 运行完整演示
python3 scripts/microservices/demo_microservices.py

# 仅检查服务状态
python3 scripts/microservices/demo_microservices.py --check-only
```

### 6. 运行测试

```bash
# 运行集成测试
python3 scripts/microservices/test_microservices.py
```

### 7. 停止服务

```bash
# 停止所有微服务
./scripts/microservices/stop_all_services.sh
```

## 📊 使用示例

### 提交处理任务

```bash
# 通过API提交ORC文件处理任务
curl -X POST "http://localhost:8001/process" \
  -H "Content-Type: application/json" \
  -d '{
    "orc_file": "/path/to/orc/file.orc",
    "process_date": "20250722",
    "prov_id": 200
  }'
```

### 查看任务状态

```bash
# 查看任务状态
curl http://localhost:8001/task/{task_id}

# 查看服务统计
curl http://localhost:8001/stats
curl http://localhost:8002/stats
```

### 监控队列状态

```bash
# 查看消息队列状态
curl http://localhost:8002/queue/status
```

## 🔧 配置说明

### 主要配置文件

- `configs/orc_processor_service/development.yaml`: ORC处理服务配置
- `configs/mongodb_writer_service/development.yaml`: MongoDB写入服务配置
- `configs/monitoring_service/development.yaml`: 监控服务配置

### 关键配置项

```yaml
# ORC处理服务
batch_processing:
  batch_size: 1000                    # 用户处理批次大小
  pid_query_batch_size: 15000         # PID查询批次大小

# MongoDB写入服务
batch_processing:
  batch_size: 10                      # 队列批量获取大小
  write_batch_size: 1000              # MongoDB写入批次大小

# 监控服务
monitoring:
  check_interval: 5                   # 检查间隔（秒）
```

## 📈 性能优势

### 与原架构对比

| 特性 | 原架构 | 新架构 |
|------|--------|--------|
| 架构模式 | 单体应用 | 微服务 |
| 并行处理 | tmux多进程 | 异步微服务 |
| 通信方式 | 进程间通信 | 消息队列 |
| 扩展性 | 垂直扩展 | 水平扩展 |
| 监控方式 | 日志监控 | API监控 |
| 故障隔离 | 无 | 服务级隔离 |

### 性能提升

- **并发能力**: 支持更高的并发处理
- **资源利用**: 更好的资源分配和利用
- **故障恢复**: 单个服务故障不影响整体
- **扩展灵活**: 可根据负载独立扩展各服务

## 🛠️ 开发和维护

### 添加新功能

1. 在相应的微服务中添加新的API端点
2. 更新配置文件
3. 添加相应的测试用例
4. 更新监控配置

### 故障排除

```bash
# 查看服务日志
tail -f logs/orc_mongodb_service/orc_processor_service/orc_processor_service.log
tail -f logs/orc_mongodb_service/mongodb_writer_service/mongodb_writer_service.log

# 检查Redis队列
redis-cli llen mongodb_write_queue

# 检查服务进程
ps aux | grep "orc_mongodb_service.orc_processor_service\|orc_mongodb_service.mongodb_writer_service"
```

## 📚 文档

- [详细架构文档](docs/MICROSERVICES_ARCHITECTURE.md)
- [原有服务文档](services/orc_mongodb_service/README.md)

## 🎉 总结

通过这次重构，我们成功实现了：

1. **架构现代化**: 从单体应用转向微服务架构
2. **性能提升**: 支持更高的并发和处理能力
3. **运维简化**: 提供完整的部署和监控工具
4. **扩展性增强**: 支持独立扩展和部署
5. **维护性改善**: 代码结构更清晰，职责分离

新架构为后续的功能扩展和性能优化奠定了坚实的基础。
