#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hive连接器

提供Hive数据库的连接和ORC文件读取功能：
- ORC文件读取
- 数据处理和转换
- 批量数据处理
- 分区数据读取

作者: User-DF Team
版本: 2.0.0
"""

import os
from pathlib import Path
from typing import Dict, List, Any, Optional, Union, Iterator, Tuple
from dataclasses import dataclass
import pandas as pd
import pyarrow as pa
import pyarrow.orc as orc
from datetime import datetime, date

from ...core import Logger, ExceptionHandler, DataValidationException, ErrorCode, ConfigManager


@dataclass
class ORCFileInfo:
    """ORC文件信息"""
    file_path: str
    file_size: int
    num_rows: int
    num_columns: int
    schema: Dict[str, str]
    created_time: Optional[datetime] = None
    partition_info: Optional[Dict[str, Any]] = None


@dataclass
class ReadOptions:
    """读取选项"""
    columns: Optional[List[str]] = None
    batch_size: Optional[int] = None
    use_threads: bool = True
    filters: Optional[List[Tuple]] = None
    chunk_size: Optional[int] = None


class HiveConnector:
    """Hive连接器"""
    
    def __init__(self, config_name: str = "hive", config_manager: Optional[ConfigManager] = None):
        """
        初始化Hive连接器
        
        Args:
            config_name: 配置名称
            config_manager: 配置管理器实例
        """
        self.config_name = config_name
        self.config_manager = config_manager or ConfigManager()
        self.logger = Logger.get_logger(f"HiveConnector.{config_name}")
        
        # 加载配置
        self._load_config()
        
        self.logger.info(f"Hive连接器初始化完成: {self.base_path}")
    
    def _load_config(self):
        """加载配置"""
        try:
            config = self.config_manager.get_config(self.config_name, default={})
            
            # 基础配置
            self.base_path = config.get("base_path", "/data/hive")
            self.default_batch_size = config.get("default_batch_size", 10000)
            self.use_threads = config.get("use_threads", True)
            
            # ORC配置
            orc_config = config.get("orc", {})
            self.orc_stripe_size = orc_config.get("stripe_size", 64 * 1024 * 1024)  # 64MB
            self.orc_compression = orc_config.get("compression", "snappy")
            
            # 分区配置
            partition_config = config.get("partitioning", {})
            self.partition_enabled = partition_config.get("enabled", True)
            self.partition_format = partition_config.get("format", "yyyymmdd")
            
        except Exception as e:
            self.logger.error(f"加载Hive配置失败: {e}")
            # 使用默认配置
            self.base_path = "/data/hive"
            self.default_batch_size = 10000
            self.use_threads = True
    
    def read_orc_file(self, file_path: str, 
                     options: Optional[ReadOptions] = None) -> pd.DataFrame:
        """
        读取ORC文件
        
        Args:
            file_path: ORC文件路径
            options: 读取选项
            
        Returns:
            DataFrame
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"ORC文件不存在: {file_path}")
            
            # 设置默认选项
            if options is None:
                options = ReadOptions()
            
            # 读取ORC文件
            orc_file = orc.ORCFile(file_path)
            
            # 构建读取参数
            read_kwargs = {}
            if options.columns:
                read_kwargs["columns"] = options.columns
            if options.use_threads is not None:
                read_kwargs["use_threads"] = options.use_threads
            
            # 读取数据
            table = orc_file.read(**read_kwargs)
            df = table.to_pandas()
            
            self.logger.info(f"成功读取ORC文件: {file_path}, 行数: {len(df)}")
            return df
            
        except Exception as e:
            self.logger.error(f"读取ORC文件失败: {file_path}, {e}")
            raise DataValidationException(
                f"读取ORC文件失败: {e}",
                ErrorCode.FILE_FORMAT_ERROR
            )
    
    def read_orc_batch(self, file_path: str, 
                      batch_size: Optional[int] = None,
                      options: Optional[ReadOptions] = None) -> Iterator[pd.DataFrame]:
        """
        批量读取ORC文件
        
        Args:
            file_path: ORC文件路径
            batch_size: 批次大小
            options: 读取选项
            
        Yields:
            DataFrame批次
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"ORC文件不存在: {file_path}")
            
            # 设置批次大小
            if batch_size is None:
                batch_size = self.default_batch_size
            
            # 设置默认选项
            if options is None:
                options = ReadOptions()
            
            # 读取ORC文件
            orc_file = orc.ORCFile(file_path)
            
            # 获取文件信息
            num_rows = orc_file.nrows
            num_batches = (num_rows + batch_size - 1) // batch_size
            
            self.logger.info(f"开始批量读取ORC文件: {file_path}, 总行数: {num_rows}, 批次数: {num_batches}")
            
            # 批量读取
            for i in range(num_batches):
                start_row = i * batch_size
                end_row = min((i + 1) * batch_size, num_rows)
                
                # 构建读取参数
                read_kwargs = {
                    "offset": start_row,
                    "length": end_row - start_row
                }
                
                if options.columns:
                    read_kwargs["columns"] = options.columns
                if options.use_threads is not None:
                    read_kwargs["use_threads"] = options.use_threads
                
                # 读取批次数据
                table = orc_file.read(**read_kwargs)
                df = table.to_pandas()
                
                self.logger.debug(f"读取批次 {i+1}/{num_batches}, 行数: {len(df)}")
                yield df
                
        except Exception as e:
            self.logger.error(f"批量读取ORC文件失败: {file_path}, {e}")
            raise DataValidationException(
                f"批量读取ORC文件失败: {e}",
                ErrorCode.FILE_FORMAT_ERROR
            )
    
    def get_orc_file_info(self, file_path: str) -> ORCFileInfo:
        """
        获取ORC文件信息
        
        Args:
            file_path: ORC文件路径
            
        Returns:
            ORC文件信息
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"ORC文件不存在: {file_path}")
            
            # 获取文件基本信息
            file_stat = os.stat(file_path)
            file_size = file_stat.st_size
            created_time = datetime.fromtimestamp(file_stat.st_ctime)
            
            # 读取ORC文件元数据
            orc_file = orc.ORCFile(file_path)
            num_rows = orc_file.nrows
            schema = orc_file.schema
            
            # 构建schema字典
            schema_dict = {}
            for i, field in enumerate(schema):
                schema_dict[field.name] = str(field.type)
            
            # 解析分区信息
            partition_info = self._parse_partition_info(file_path)
            
            return ORCFileInfo(
                file_path=file_path,
                file_size=file_size,
                num_rows=num_rows,
                num_columns=len(schema_dict),
                schema=schema_dict,
                created_time=created_time,
                partition_info=partition_info
            )
            
        except Exception as e:
            self.logger.error(f"获取ORC文件信息失败: {file_path}, {e}")
            raise DataValidationException(
                f"获取ORC文件信息失败: {e}",
                ErrorCode.FILE_FORMAT_ERROR
            )
    
    def list_orc_files(self, directory: str, 
                      pattern: Optional[str] = None,
                      recursive: bool = True) -> List[str]:
        """
        列出目录中的ORC文件
        
        Args:
            directory: 目录路径
            pattern: 文件名模式
            recursive: 是否递归搜索
            
        Returns:
            ORC文件路径列表
        """
        try:
            orc_files = []
            directory_path = Path(directory)
            
            if not directory_path.exists():
                self.logger.warning(f"目录不存在: {directory}")
                return orc_files
            
            # 搜索ORC文件
            if recursive:
                search_pattern = "**/*.orc"
            else:
                search_pattern = "*.orc"
            
            for file_path in directory_path.glob(search_pattern):
                if file_path.is_file():
                    # 应用文件名模式过滤
                    if pattern is None or pattern in file_path.name:
                        orc_files.append(str(file_path))
            
            self.logger.info(f"找到 {len(orc_files)} 个ORC文件在目录: {directory}")
            return sorted(orc_files)
            
        except Exception as e:
            self.logger.error(f"列出ORC文件失败: {directory}, {e}")
            return []
    
    def read_partition_data(self, table_path: str, 
                           partition_date: Union[str, date],
                           options: Optional[ReadOptions] = None) -> pd.DataFrame:
        """
        读取分区数据
        
        Args:
            table_path: 表路径
            partition_date: 分区日期
            options: 读取选项
            
        Returns:
            DataFrame
        """
        try:
            # 转换分区日期格式
            if isinstance(partition_date, date):
                partition_str = partition_date.strftime("%Y%m%d")
            elif isinstance(partition_date, str):
                partition_str = partition_date
            else:
                raise ValueError(f"不支持的分区日期格式: {type(partition_date)}")
            
            # 构建分区路径
            partition_path = os.path.join(table_path, f"dt={partition_str}")
            
            if not os.path.exists(partition_path):
                raise FileNotFoundError(f"分区路径不存在: {partition_path}")
            
            # 查找分区中的ORC文件
            orc_files = self.list_orc_files(partition_path, recursive=False)
            
            if not orc_files:
                raise FileNotFoundError(f"分区中没有找到ORC文件: {partition_path}")
            
            # 读取所有ORC文件并合并
            dataframes = []
            for orc_file in orc_files:
                df = self.read_orc_file(orc_file, options)
                dataframes.append(df)
            
            # 合并数据
            if len(dataframes) == 1:
                result_df = dataframes[0]
            else:
                result_df = pd.concat(dataframes, ignore_index=True)
            
            self.logger.info(f"成功读取分区数据: {partition_path}, 行数: {len(result_df)}")
            return result_df
            
        except Exception as e:
            self.logger.error(f"读取分区数据失败: {table_path}, {partition_date}, {e}")
            raise DataValidationException(
                f"读取分区数据失败: {e}",
                ErrorCode.FILE_NOT_FOUND
            )
    
    def _parse_partition_info(self, file_path: str) -> Optional[Dict[str, Any]]:
        """
        解析分区信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            分区信息字典
        """
        try:
            path_parts = Path(file_path).parts
            partition_info = {}
            
            # 查找分区键值对
            for part in path_parts:
                if "=" in part:
                    key, value = part.split("=", 1)
                    partition_info[key] = value
            
            return partition_info if partition_info else None
            
        except Exception as e:
            self.logger.debug(f"解析分区信息失败: {file_path}, {e}")
            return None
    
    def validate_orc_file(self, file_path: str) -> bool:
        """
        验证ORC文件
        
        Args:
            file_path: ORC文件路径
            
        Returns:
            验证结果
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                return False
            
            # 尝试读取文件元数据
            orc_file = orc.ORCFile(file_path)
            schema = orc_file.schema
            num_rows = orc_file.nrows
            
            # 基本验证
            if num_rows < 0:
                return False
            
            if len(schema) == 0:
                return False
            
            self.logger.debug(f"ORC文件验证通过: {file_path}")
            return True
            
        except Exception as e:
            self.logger.warning(f"ORC文件验证失败: {file_path}, {e}")
            return False
    
    def get_table_partitions(self, table_path: str) -> List[str]:
        """
        获取表的所有分区
        
        Args:
            table_path: 表路径
            
        Returns:
            分区列表
        """
        try:
            partitions = []
            table_path_obj = Path(table_path)
            
            if not table_path_obj.exists():
                self.logger.warning(f"表路径不存在: {table_path}")
                return partitions
            
            # 查找分区目录
            for item in table_path_obj.iterdir():
                if item.is_dir() and "=" in item.name:
                    partitions.append(item.name)
            
            return sorted(partitions)
            
        except Exception as e:
            self.logger.error(f"获取表分区失败: {table_path}, {e}")
            return []
