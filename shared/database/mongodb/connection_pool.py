#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MongoDB连接池管理器

提供MongoDB的统一连接池管理功能：
- 连接池管理和复用
- 自动重连和故障恢复
- 分片支持
- 读写分离
- 连接监控和统计

作者: User-DF Team
版本: 2.0.0
"""

import threading
import time
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass
from pymongo import MongoClient
from pymongo.database import Database
from pymongo.collection import Collection
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError
import logging

from ...core import ConfigManager, Logger, ExceptionHandler, MongoDBException, ErrorCode


@dataclass
class ConnectionInfo:
    """连接信息"""
    client: MongoClient
    database: Database
    created_at: float
    last_used: float
    use_count: int = 0
    is_healthy: bool = True


@dataclass
class PoolStats:
    """连接池统计信息"""
    total_connections: int = 0
    active_connections: int = 0
    idle_connections: int = 0
    failed_connections: int = 0
    total_requests: int = 0
    failed_requests: int = 0


class MongoDBPool:
    """MongoDB连接池管理器"""
    
    _instances: Dict[str, 'MongoDBPool'] = {}
    _lock = threading.Lock()
    
    def __new__(cls, config_name: str = "mongodb", config_manager: Optional[ConfigManager] = None):
        """单例模式，每个配置名称对应一个实例"""
        if config_name not in cls._instances:
            with cls._lock:
                if config_name not in cls._instances:
                    instance = super().__new__(cls)
                    cls._instances[config_name] = instance
        return cls._instances[config_name]
    
    def __init__(self, config_name: str = "mongodb", config_manager: Optional[ConfigManager] = None):
        """
        初始化MongoDB连接池
        
        Args:
            config_name: 配置名称
            config_manager: 配置管理器实例
        """
        if hasattr(self, '_initialized'):
            return
            
        self.config_name = config_name
        self.config_manager = config_manager or ConfigManager()
        self.logger = Logger.get_logger(f"MongoDBPool.{config_name}")
        
        # 加载配置
        self._load_config()

        # 连接池管理
        self._connections: Dict[str, ConnectionInfo] = {}
        self._connection_lock = threading.RLock()
        self._stats = PoolStats()

        # 写入优化配置
        self._write_optimization_config = None
        
        # 健康检查
        self._health_check_interval = 60  # 秒
        self._last_health_check = 0
        
        # 初始化连接池
        self._initialize_pool()
        
        self._initialized = True
        self.logger.info(f"MongoDB连接池初始化完成: {self.database_name}")
    
    def _load_config(self):
        """加载配置"""
        try:
            config = self.config_manager.get_config(self.config_name, default={})
            
            # 连接配置
            connection_config = config.get("connection", {})
            self.use_uri = connection_config.get("use_uri", False)
            
            if self.use_uri:
                self.uri = connection_config["uri"]
            else:
                self.host = connection_config.get("host", "localhost")
                self.port = connection_config.get("port", 27017)
                self.username = connection_config.get("username")
                self.password = connection_config.get("password")
            
            # 数据库名称
            self.database_name = config.get("database", "nrdc")
            
            # 连接池配置
            pool_config = config.get("connection_pool", {})
            self.max_pool_size = pool_config.get("max_pool_size", 100)
            self.min_pool_size = pool_config.get("min_pool_size", 10)
            self.max_idle_time_ms = pool_config.get("max_idle_time_ms", 30000)
            
            # 超时配置
            timeout_config = config.get("timeouts", {})
            self.connect_timeout_ms = timeout_config.get("connect_timeout_ms", 10000)
            self.server_selection_timeout_ms = timeout_config.get("server_selection_timeout_ms", 30000)
            self.socket_timeout_ms = timeout_config.get("socket_timeout_ms", 60000)

            # 写入优化配置
            self._write_optimization_config = config.get("write_optimization", {})

            # 写确认配置
            write_concern_config = self._write_optimization_config.get("write_concern", {})
            self.write_concern_w = write_concern_config.get("w", 1)
            self.write_concern_j = write_concern_config.get("j", False)
            self.write_concern_wtimeout = write_concern_config.get("wtimeout", 5000)

            # 批量操作配置
            bulk_config = self._write_optimization_config.get("bulk_operations", {})
            self.unordered_inserts = bulk_config.get("unordered_inserts", True)
            self.bypass_document_validation = bulk_config.get("bypass_document_validation", False)

        except Exception as e:
            self.logger.error(f"加载MongoDB配置失败: {e}")
            raise MongoDBException(
                f"MongoDB配置加载失败: {e}",
                ErrorCode.MONGODB_CONNECTION_ERROR
            )
    
    def _initialize_pool(self):
        """初始化连接池"""
        try:
            # 创建最小数量的连接
            for i in range(self.min_pool_size):
                connection_id = f"conn_{i}"
                self._create_connection(connection_id)
            
            self.logger.info(f"MongoDB连接池初始化完成，创建了 {self.min_pool_size} 个连接")
            
        except Exception as e:
            self.logger.error(f"初始化MongoDB连接池失败: {e}")
            raise MongoDBException(
                f"MongoDB连接池初始化失败: {e}",
                ErrorCode.MONGODB_CONNECTION_ERROR
            )
    
    def _create_connection(self, connection_id: str) -> ConnectionInfo:
        """创建新连接"""
        try:
            # 构建连接字符串
            if self.use_uri:
                uri = self.uri
            else:
                if self.username and self.password:
                    uri = f"mongodb://{self.username}:{self.password}@{self.host}:{self.port}/"
                else:
                    uri = f"mongodb://{self.host}:{self.port}/"
            
            # 客户端选项
            client_options = {
                "maxPoolSize": self.max_pool_size,
                "minPoolSize": self.min_pool_size,
                "maxIdleTimeMS": self.max_idle_time_ms,
                "connectTimeoutMS": self.connect_timeout_ms,
                "serverSelectionTimeoutMS": self.server_selection_timeout_ms,
                "socketTimeoutMS": self.socket_timeout_ms,
                "retryWrites": True,
                "retryReads": True,
                # 使用配置的写确认级别
                "w": self.write_concern_w,
                "journal": self.write_concern_j,
                "wtimeoutMS": self.write_concern_wtimeout,
                "readPreference": "secondaryPreferred"
            }
            
            # 创建客户端和数据库连接
            client = MongoClient(uri, **client_options)
            database = client[self.database_name]
            
            # 测试连接
            database.command("ping")
            
            # 创建连接信息
            connection_info = ConnectionInfo(
                client=client,
                database=database,
                created_at=time.time(),
                last_used=time.time()
            )
            
            with self._connection_lock:
                self._connections[connection_id] = connection_info
                self._stats.total_connections += 1
            
            self.logger.debug(f"创建MongoDB连接成功: {connection_id}")
            return connection_info
            
        except Exception as e:
            self.logger.error(f"创建MongoDB连接失败: {e}")
            with self._connection_lock:
                self._stats.failed_connections += 1
            raise MongoDBException(
                f"创建MongoDB连接失败: {e}",
                ErrorCode.MONGODB_CONNECTION_ERROR
            )
    
    def get_connection(self, connection_id: Optional[str] = None) -> ConnectionInfo:
        """
        获取数据库连接
        
        Args:
            connection_id: 指定连接ID，如果不指定则自动选择
            
        Returns:
            连接信息
        """
        with self._connection_lock:
            self._stats.total_requests += 1
            
            # 健康检查
            self._check_connections_health()
            
            # 如果指定了连接ID
            if connection_id and connection_id in self._connections:
                connection = self._connections[connection_id]
                if connection.is_healthy:
                    connection.last_used = time.time()
                    connection.use_count += 1
                    self._stats.active_connections += 1
                    return connection
            
            # 查找可用连接
            for conn_id, connection in self._connections.items():
                if connection.is_healthy:
                    connection.last_used = time.time()
                    connection.use_count += 1
                    self._stats.active_connections += 1
                    return connection
            
            # 如果没有可用连接且未达到最大连接数，创建新连接
            if len(self._connections) < self.max_pool_size:
                new_conn_id = f"conn_{len(self._connections)}"
                return self._create_connection(new_conn_id)
            
            # 连接池已满，抛出异常
            self._stats.failed_requests += 1
            raise MongoDBException(
                "MongoDB连接池已满，无法获取连接",
                ErrorCode.MONGODB_CONNECTION_ERROR
            )
    
    def get_database(self, connection_id: Optional[str] = None) -> Database:
        """
        获取数据库对象
        
        Args:
            connection_id: 连接ID
            
        Returns:
            数据库对象
        """
        connection = self.get_connection(connection_id)
        return connection.database
    
    def get_collection(self, collection_name: str, 
                      connection_id: Optional[str] = None) -> Collection:
        """
        获取集合对象
        
        Args:
            collection_name: 集合名称
            connection_id: 连接ID
            
        Returns:
            集合对象
        """
        database = self.get_database(connection_id)
        return database[collection_name]
    
    def _check_connections_health(self):
        """检查连接健康状态"""
        current_time = time.time()
        
        # 限制健康检查频率
        if current_time - self._last_health_check < self._health_check_interval:
            return
        
        self._last_health_check = current_time
        
        unhealthy_connections = []
        
        for conn_id, connection in self._connections.items():
            try:
                # 执行ping命令测试连接
                connection.database.command("ping")
                connection.is_healthy = True
            except Exception as e:
                self.logger.warning(f"连接 {conn_id} 健康检查失败: {e}")
                connection.is_healthy = False
                unhealthy_connections.append(conn_id)
        
        # 移除不健康的连接
        for conn_id in unhealthy_connections:
            self._remove_connection(conn_id)
    
    def _remove_connection(self, connection_id: str):
        """移除连接"""
        if connection_id in self._connections:
            try:
                connection = self._connections[connection_id]
                connection.client.close()
            except Exception as e:
                # 检查是否是Python关闭时的错误
                import sys
                if sys.meta_path is None:
                    # Python正在关闭，忽略这些错误
                    pass
                else:
                    self.logger.warning(f"关闭连接 {connection_id} 时发生错误: {e}")
            
            del self._connections[connection_id]
            self._stats.total_connections -= 1
            
            # 只有在Python没有关闭时才记录日志
            import sys
            if sys.meta_path is not None:
                self.logger.info(f"移除不健康的连接: {connection_id}")
    
    def close_all_connections(self):
        """关闭所有连接"""
        try:
            with self._connection_lock:
                for conn_id, connection in self._connections.items():
                    try:
                        connection.client.close()
                    except Exception as e:
                        # 检查是否是Python关闭时的错误
                        import sys
                        if sys.meta_path is None:
                            # Python正在关闭，忽略这些错误
                            pass
                        else:
                            self.logger.warning(f"关闭连接 {conn_id} 时发生错误: {e}")
                
                self._connections.clear()
                self._stats = PoolStats()
                
                # 只有在Python没有关闭时才记录日志
                import sys
                if sys.meta_path is not None:
                    self.logger.info("所有MongoDB连接已关闭")
        except Exception:
            # 在Python关闭时可能出现各种异常，静默处理
            pass
    
    def get_stats(self) -> PoolStats:
        """获取连接池统计信息"""
        with self._connection_lock:
            self._stats.idle_connections = len([
                conn for conn in self._connections.values()
                if conn.is_healthy and time.time() - conn.last_used > 60
            ])
            return self._stats

    def get_write_optimization_config(self) -> Dict[str, Any]:
        """
        获取写入优化配置

        Returns:
            写入优化配置字典
        """
        return {
            "write_concern": {
                "w": self.write_concern_w,
                "j": self.write_concern_j,
                "wtimeout": self.write_concern_wtimeout
            },
            "bulk_operations": {
                "unordered_inserts": self.unordered_inserts,
                "bypass_document_validation": self.bypass_document_validation
            }
        }
    
    def __del__(self):
        """析构函数"""
        try:
            # 检查Python是否正在关闭
            import sys
            if sys.meta_path is not None:
                self.close_all_connections()
        except:
            # 在Python关闭时可能出现各种异常，静默处理
            pass
