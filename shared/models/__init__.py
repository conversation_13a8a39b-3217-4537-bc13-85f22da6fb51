"""
数据模型模块

定义项目中使用的数据模型：
- 用户模型
- 向量模型
- 配置模型
"""

from dataclasses import dataclass
from typing import List, Optional, Dict, Any

@dataclass
class UserModel:
    """用户模型"""
    uid: int
    pid_list: List[str]
    pid_count: int
    updated_days: int
    is_stored: bool = False
    stored_at_days: Optional[int] = None
    provid: Optional[str] = None
    pid_timestamps: Optional[Dict[str, Any]] = None

@dataclass
class VectorModel:
    """向量模型"""
    id: Any
    vector: List[float]
    metadata: Optional[Dict[str, Any]] = None

__all__ = [
    "UserModel",
    "VectorModel"
]
