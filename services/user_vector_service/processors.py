#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
向量处理器和PCA模型管理器

专门处理用户向量计算和PCA模型管理：
- 向量聚合和权重计算
- 预计算PCA模型加载和使用
- 向量降维和标准化

作者: User-DF Team
版本: 2.0.0
"""

from typing import List, Any, Optional, Dict
from dataclasses import dataclass
import numpy as np
from sklearn.preprocessing import StandardScaler

from shared.core import Logger, ConfigManager
from shared.utils import TimeUtils
from services.user_vector_service.pca_model_loader import PrecomputedPCALoader


@dataclass
class ModelStatus:
    """模型状态"""
    is_loaded: bool = False
    model_path: Optional[str] = None
    n_components: int = 0
    n_samples_seen: int = 0
    explained_variance_ratio: Optional[float] = None
    last_saved: Optional[str] = None
    model_size_mb: float = 0.0


class VectorProcessor:
    """向量处理器"""
    
    def __init__(self, config_manager: ConfigManager, config: Any):
        """
        初始化向量处理器
        
        Args:
            config_manager: 配置管理器
            config: 服务配置
        """
        self.config_manager = config_manager
        self.config = config
        self.logger = Logger.get_logger("VectorProcessor")
        
        # 加载向量计算配置
        self._load_vector_config()
        
        # 初始化标准化器
        self.scaler = StandardScaler()

        # 初始化PCA管理器
        self.pca_manager = PCAModelManager(config_manager, config)

        self.logger.debug("向量处理器初始化完成")
    
    def _load_vector_config(self):
        """加载向量计算配置"""
        try:
            vector_config = self.config_manager.get_config("user_vector_service", default={})
            
            # 聚合配置
            self.aggregation_method = vector_config.get("aggregation_method", "weighted_mean")
            self.weight_strategy = vector_config.get("weight_strategy", "recency")
            self.min_pids_required = self.config.min_pids_required
            self.max_pids_for_computation = self.config.max_pids_for_computation
            
            # 时间权重配置
            self.time_decay_factor = vector_config.get("time_decay_factor", 0.1)
            self.max_time_weight = vector_config.get("max_time_weight", 2.0)
            
        except Exception as e:
            self.logger.error(f"加载向量计算配置失败: {e}")
            # 使用默认配置
            self.aggregation_method = "weighted_mean"
            self.weight_strategy = "recency"
    
    def compute_user_vector(self, content_vectors: List[List[float]],
                           timestamps: Optional[List[int]] = None,
                           pid_frequencies: Optional[List[int]] = None) -> Optional[List[float]]:
        """
        计算用户向量

        Args:
            content_vectors: 内容向量列表（512维）
            timestamps: 时间戳列表（天数格式，可选）
            pid_frequencies: PID频率列表（可选）

        Returns:
            用户向量（降维后的维度），如果计算失败返回None
        """
        try:
            if not content_vectors:
                self.logger.debug("内容向量列表为空，无法计算用户向量")
                return None

            if len(content_vectors) < self.min_pids_required:
                self.logger.debug(f"内容向量数量不足（{len(content_vectors)} < {self.min_pids_required}），跳过计算")
                return None

            # 限制向量数量
            if len(content_vectors) > self.max_pids_for_computation:
                # 如果有时间戳，按时间排序保留最新的
                if timestamps and len(timestamps) == len(content_vectors):
                    sorted_pairs = sorted(
                        zip(content_vectors, timestamps),
                        key=lambda x: x[1],
                        reverse=True
                    )
                    content_vectors = [pair[0] for pair in sorted_pairs[:self.max_pids_for_computation]]
                    timestamps = [pair[1] for pair in sorted_pairs[:self.max_pids_for_computation]]
                else:
                    content_vectors = content_vectors[:self.max_pids_for_computation]
                    if timestamps:
                        timestamps = timestamps[:self.max_pids_for_computation]
                    if pid_frequencies:
                        pid_frequencies = pid_frequencies[:self.max_pids_for_computation]

            # 转换为numpy数组
            vectors_array = np.array(content_vectors, dtype=np.float32)

            # 验证向量维度
            if vectors_array.shape[1] != self.config.source_dimension:
                self.logger.warning(f"向量维度不匹配: {vectors_array.shape[1]} != {self.config.source_dimension}")
                return None

            # 先对每个内容向量进行PCA降维
            reduced_vectors = []
            for i, content_vector in enumerate(content_vectors):
                reduced_vector = self.pca_manager.transform_vector(content_vector)
                if reduced_vector is None:
                    self.logger.warning(f"第{i+1}个内容向量PCA降维失败，跳过该向量")
                    continue
                reduced_vectors.append(reduced_vector)

            if not reduced_vectors:
                self.logger.warning("所有内容向量PCA降维都失败")
                return None

            if len(reduced_vectors) < self.min_pids_required:
                self.logger.debug(f"PCA降维后向量数量不足（{len(reduced_vectors)} < {self.min_pids_required}），跳过计算")
                return None

            # 转换为numpy数组（现在是降维后的向量）
            reduced_vectors_array = np.array(reduced_vectors, dtype=np.float32)

            # 重新计算权重（基于有效的降维向量数量）
            effective_count = len(reduced_vectors)
            if timestamps and len(timestamps) > effective_count:
                timestamps = timestamps[:effective_count]
            if pid_frequencies and len(pid_frequencies) > effective_count:
                pid_frequencies = pid_frequencies[:effective_count]

            weights = self._compute_weights(effective_count, timestamps, pid_frequencies)

            # 对降维后的向量进行加权聚合
            final_vector = self._aggregate_vectors(reduced_vectors_array, weights)

            # 标准化最终向量
            normalized_vector = self._normalize_vector(final_vector)

            return normalized_vector.tolist()
            
        except Exception as e:
            self.logger.error(f"计算用户向量时发生错误: {e}")
            return None
    
    def _compute_weights(self, num_vectors: int, 
                        timestamps: Optional[List[int]] = None,
                        pid_frequencies: Optional[List[int]] = None) -> np.ndarray:
        """
        计算权重
        
        Args:
            num_vectors: 向量数量
            timestamps: 时间戳列表
            pid_frequencies: PID频率列表
            
        Returns:
            权重数组
        """
        if self.weight_strategy == "uniform":
            return np.ones(num_vectors, dtype=np.float32)
        
        elif self.weight_strategy == "recency" and timestamps:
            # 基于时间的权重
            current_days = TimeUtils.date_to_days(TimeUtils.today())
            time_diffs = np.array([current_days - ts for ts in timestamps], dtype=np.float32)
            
            # 使用指数衰减
            weights = np.exp(-self.time_decay_factor * time_diffs)
            
            # 限制最大权重
            weights = np.clip(weights, 0.1, self.max_time_weight)
            
            return weights
        
        elif self.weight_strategy == "frequency" and pid_frequencies:
            # 基于频率的权重
            frequencies = np.array(pid_frequencies, dtype=np.float32)
            weights = np.log1p(frequencies)  # log(1 + freq)
            return weights / np.sum(weights) * len(frequencies)  # 归一化
        
        else:
            # 默认使用均匀权重
            return np.ones(num_vectors, dtype=np.float32)
    
    def _aggregate_vectors(self, vectors: np.ndarray, weights: np.ndarray) -> np.ndarray:
        """
        聚合向量（使用权重）

        Args:
            vectors: 向量数组 (n_vectors, n_dimensions)
            weights: 权重数组 (n_vectors,)

        Returns:
            聚合后的向量
        """
        if self.aggregation_method == "mean":
            return np.mean(vectors, axis=0)

        elif self.aggregation_method == "weighted_mean":
            return np.average(vectors, axis=0, weights=weights)

        elif self.aggregation_method == "max_pooling":
            return np.max(vectors, axis=0)

        elif self.aggregation_method == "min_pooling":
            return np.min(vectors, axis=0)

        else:
            self.logger.warning(f"未知的聚合方法: {self.aggregation_method}，使用加权平均")
            return np.average(vectors, axis=0, weights=weights)
    
    def _normalize_vector(self, vector: np.ndarray) -> np.ndarray:
        """
        标准化向量

        Args:
            vector: 输入向量

        Returns:
            标准化后的向量
        """
        try:
            # L2标准化
            norm = np.linalg.norm(vector)
            if norm > 0:
                return vector / norm
            else:
                return vector
        except Exception as e:
            self.logger.warning(f"向量标准化失败: {e}")
            return vector


class PCAModelManager:
    """PCA模型管理器（使用预计算模型）"""

    def __init__(self, config_manager: ConfigManager, config: Any):
        """
        初始化PCA模型管理器

        Args:
            config_manager: 配置管理器
            config: 服务配置
        """
        self.config_manager = config_manager
        self.config = config
        self.logger = Logger.get_logger("PCAModelManager")

        # 使用预计算PCA模型加载器
        self.pca_loader = PrecomputedPCALoader(config_manager, config)

        # 兼容性属性
        self.target_dimension = getattr(config, 'target_dimension', 256)
        self.source_dimension = getattr(config, 'source_dimension', 512)

        self.logger.debug("PCA模型管理器初始化完成（使用预计算模型）")
    
    def train_incremental_batch(self, content_vectors: List[List[float]]) -> bool:
        """
        增量训练PCA模型（已移除，仅保留兼容性接口）

        Args:
            content_vectors: 内容向量列表（未使用）

        Returns:
            总是返回True（不再支持增量训练）
        """
        self.logger.debug("增量PCA训练功能已移除，使用预计算PCA模型")
        return True
    
    def transform_vector(self, vector: List[float]) -> Optional[List[float]]:
        """
        使用PCA模型转换向量

        Args:
            vector: 输入向量

        Returns:
            转换后的向量或None
        """
        return self.pca_loader.transform_vector(vector)
    
    def save_model(self) -> bool:
        """
        保存PCA模型（已移除，仅保留兼容性接口）

        Returns:
            总是返回True（不再支持模型保存）
        """
        self.logger.debug("PCA模型保存功能已移除，使用预计算PCA模型")
        return True

    def load_model(self) -> bool:
        """
        加载PCA模型（委托给预计算模型加载器）

        Returns:
            加载是否成功
        """
        return self.pca_loader.reload_model()

    def get_model_status(self) -> Dict[str, Any]:
        """
        获取模型状态

        Returns:
            模型状态
        """
        model_info = self.pca_loader.get_model_info()

        # 转换为兼容的格式
        if model_info.get('loaded', False):
            return {
                'is_loaded': True,
                'model_path': 'precomputed_model',
                'n_components': model_info.get('target_dimension', self.target_dimension),
                'n_samples_seen': 'N/A (precomputed)',
                'explained_variance_ratio': model_info.get('validation_results', {}).get('explained_variance_ratio'),
                'last_saved': model_info.get('loaded_at'),
                'model_size_mb': 'N/A'
            }
        else:
            return {
                'is_loaded': False,
                'error': model_info.get('error', 'Unknown error')
            }
