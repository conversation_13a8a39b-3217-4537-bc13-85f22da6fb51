#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MongoDB用户数据查询模块

专门处理用户数据的MongoDB查询：
- 查询需要处理的用户
- 获取用户PID历史记录
- 过滤和排序用户数据
- 支持日期范围和条件过滤

作者: User-DF Team
版本: 2.0.0
"""

from typing import Dict, List, Any, Optional, Tuple, Iterator
from dataclasses import dataclass

from shared.core import Logger, ConfigManager, DataValidator
from shared.database.mongodb import MongoDBPool, MongoDBOperations, MongoDBQueryBuilder
from shared.database.mongodb.operations import QueryOptions
from shared.utils import TimeUtils


@dataclass
class UserQueryConfig:
    """用户查询配置"""
    collection_name: str
    batch_size: int
    max_pids_per_user: int
    min_pids_required: int
    # 日期范围过滤配置
    date_range_enabled: bool
    start_date: Optional[str]
    end_date: Optional[str]
    recent_days: Optional[int]
    # 向量状态过滤配置
    only_not_stored: bool
    include_expired: bool
    # 省份过滤配置
    province_enabled: bool
    prov_ids: List[str]
    # 查询排序配置
    sort_by_update_time: bool
    sort_direction: str
    # ID范围批次处理配置
    id_range_enabled: bool
    id_range_span: int
    start_id: Optional[int]
    end_id: Optional[int]


class UserDataQuery:
    """MongoDB用户数据查询"""
    
    def __init__(self, mongodb_pool: MongoDBPool, 
                 config_manager: ConfigManager, 
                 service_config: Any):
        """
        初始化用户数据查询
        
        Args:
            mongodb_pool: MongoDB连接池
            config_manager: 配置管理器
            service_config: 服务配置
        """
        self.mongodb_pool = mongodb_pool
        self.config_manager = config_manager
        self.service_config = service_config
        self.logger = Logger.get_logger("UserDataQuery")
        
        # 加载查询配置
        self._load_query_config()
        
        # 初始化MongoDB操作
        self.mongodb_ops = MongoDBOperations(
            self.mongodb_pool, 
            self.query_config.collection_name
        )
        
        self.logger.debug("MongoDB用户数据查询初始化完成")

    def get_id_range_bounds(self) -> Tuple[Optional[int], Optional[int]]:
        """
        获取集合中_id的范围边界

        Returns:
            (min_id, max_id) 元组
        """
        try:
            # 获取最小_id
            min_options = QueryOptions(
                sort=[("_id", 1)],
                limit=1,
                projection={"_id": 1}
            )
            min_result = list(self.mongodb_ops.find_many({}, options=min_options))
            min_id = min_result[0]["_id"] if min_result else None

            # 获取最大_id
            max_options = QueryOptions(
                sort=[("_id", -1)],
                limit=1,
                projection={"_id": 1}
            )
            max_result = list(self.mongodb_ops.find_many({}, options=max_options))
            max_id = max_result[0]["_id"] if max_result else None

            self.logger.debug(f"集合_id范围: {min_id} - {max_id}")
            return min_id, max_id

        except Exception as e:
            self.logger.error(f"获取_id范围边界失败: {e}")
            return None, None

    def get_users_by_id_range(self, start_id: int, end_id: int,
                             user_limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        按_id范围获取用户数据

        Args:
            start_id: 起始_id（包含）
            end_id: 结束_id（不包含）
            user_limit: 用户数量限制

        Returns:
            用户数据列表
        """
        try:
            self.logger.debug(f"按_id范围查询用户: {start_id} - {end_id}")

            # 构建查询条件
            query_builder = MongoDBQueryBuilder()

            # _id范围过滤
            query_builder.filter_dict({
                "_id": {
                    "$gte": start_id,
                    "$lt": end_id
                }
            })

            # 应用其他过滤条件
            self._apply_common_filters(query_builder)

            # 排序：按_id升序
            query_builder.sort_ascending("_id")

            # 限制数量
            if user_limit:
                query_builder.limit_results(user_limit)

            # 投影字段
            self._apply_projection(query_builder)

            # 执行查询
            # 创建QueryOptions对象
            query_options = QueryOptions(
                projection=query_builder.get_projection() if query_builder.get_projection() else None,
                sort=query_builder.get_sort() if query_builder.get_sort() else None,
                limit=query_builder._limit,
                skip=query_builder._skip,
                hint=query_builder._hint
            )
            
            users = list(self.mongodb_ops.find_many(
                query_builder.get_filter(),
                options=query_options
            ))

            self.logger.debug(f"找到 {len(users)} 个用户需要处理")
            return users

        except Exception as e:
            self.logger.error(f"按_id范围查询用户失败: {e}")
            return []

    def _apply_common_filters(self, query_builder: MongoDBQueryBuilder):
        """
        应用通用过滤条件

        Args:
            query_builder: 查询构建器
        """
        # PID数量过滤
        query_builder.greater_than_or_equal("pid_count", self.query_config.min_pids_required)

    def _apply_projection(self, query_builder: MongoDBQueryBuilder):
        """
        应用投影字段

        Args:
            query_builder: 查询构建器
        """
        # 投影字段：包含_id、pid_groups、updated_days、vector_status等
        query_builder.include_fields(
            "_id", "pid_groups", "pid_count", "updated_days",
            "vector_status", "prov_id"
        )

    def get_users_by_id_ranges(self, user_limit: Optional[int] = None) -> Iterator[List[Dict[str, Any]]]:
        """
        按_id范围批量获取用户数据（生成器）

        Args:
            user_limit: 总用户数量限制

        Yields:
            每个范围的用户数据列表
        """
        try:
            if not self.query_config.id_range_enabled:
                self.logger.warning("_id范围查询未启用")
                return

            # 获取_id范围边界
            min_id, max_id = self.get_id_range_bounds()
            if min_id is None or max_id is None:
                self.logger.warning("无法获取_id范围边界")
                return

            # 检查ID类型是否一致
            if type(min_id) != type(max_id):
                self.logger.warning(f"_id类型不一致: {type(min_id).__name__} vs {type(max_id).__name__}")
                self.logger.info("将使用简单的批次查询而非范围查询")
                # 回退到简单的批次查询
                yield from self._get_users_simple_batch(user_limit)
                return

            # 只有当ID都是整数时才进行范围查询
            if not isinstance(min_id, int) or not isinstance(max_id, int):
                self.logger.warning(f"_id不是整数类型: {type(min_id).__name__}")
                self.logger.info("将使用简单的批次查询而非范围查询")
                # 回退到简单的批次查询
                yield from self._get_users_simple_batch(user_limit)
                return

            # 使用配置的起始和结束_id
            start_id = self.query_config.start_id if self.query_config.start_id is not None else min_id
            end_id = self.query_config.end_id if self.query_config.end_id is not None else max_id + 1

            self.logger.info(f"按_id范围处理用户: {start_id} - {end_id}, 跨度: {self.query_config.id_range_span}")

            total_processed = 0
            current_start = start_id

            while current_start < end_id:
                current_end = min(current_start + self.query_config.id_range_span, end_id)

                # 计算当前批次的用户限制
                batch_limit = None
                if user_limit:
                    remaining_limit = user_limit - total_processed
                    if remaining_limit <= 0:
                        break
                    batch_limit = remaining_limit

                # 获取当前范围的用户
                users = self.get_users_by_id_range(current_start, current_end, batch_limit)

                if users:
                    total_processed += len(users)
                    self.logger.debug(f"_id范围 {current_start}-{current_end}: 获取到 {len(users)} 个用户")
                    yield users

                    # 检查是否达到总限制
                    if user_limit and total_processed >= user_limit:
                        break
                else:
                    self.logger.debug(f"_id范围 {current_start}-{current_end}: 没有找到用户")

                current_start = current_end

            self.logger.info(f"_id范围查询完成: {total_processed} 个用户")

        except Exception as e:
            self.logger.error(f"按_id范围批量获取用户失败: {e}")
            return

    def get_users_by_id_ranges_with_progress(self, user_limit: Optional[int] = None) -> Iterator[Tuple[List[Dict[str, Any]], Dict[str, Any]]]:
        """
        按_id范围批量获取用户数据（生成器，带进度信息）

        Args:
            user_limit: 总用户数量限制

        Yields:
            (用户数据列表, 批次信息) 元组
        """
        try:
            if not self.query_config.id_range_enabled:
                self.logger.warning("_id范围查询未启用")
                return

            # 获取_id范围边界
            min_id, max_id = self.get_id_range_bounds()
            if min_id is None or max_id is None:
                self.logger.warning("无法获取_id范围边界")
                return

            # 检查ID类型是否一致
            if type(min_id) != type(max_id):
                self.logger.warning(f"_id类型不一致: {type(min_id).__name__} vs {type(max_id).__name__}")
                self.logger.info("将使用简单的批次查询而非范围查询")
                # 回退到简单的批次查询
                yield from self._get_users_simple_batch_with_progress(user_limit)
                return

            # 只有当ID都是整数时才进行范围查询
            if not isinstance(min_id, int) or not isinstance(max_id, int):
                self.logger.warning(f"_id不是整数类型: {type(min_id).__name__}")
                self.logger.info("将使用简单的批次查询而非范围查询")
                # 回退到简单的批次查询
                yield from self._get_users_simple_batch_with_progress(user_limit)
                return

            # 使用配置的起始和结束_id
            start_id = self.query_config.start_id if self.query_config.start_id is not None else min_id
            end_id = self.query_config.end_id if self.query_config.end_id is not None else max_id + 1

            self.logger.info(f"按_id范围处理用户: {start_id} - {end_id}, 跨度: {self.query_config.id_range_span}")

            total_processed = 0
            current_start = start_id
            batch_number = 0

            while current_start < end_id:
                current_end = min(current_start + self.query_config.id_range_span, end_id)
                batch_number += 1

                # 计算当前批次的用户限制
                batch_limit = None
                if user_limit:
                    remaining_limit = user_limit - total_processed
                    if remaining_limit <= 0:
                        break
                    batch_limit = remaining_limit

                # 获取当前范围的用户
                users = self.get_users_by_id_range(current_start, current_end, batch_limit)

                # 构建批次信息
                batch_info = {
                    "start_id": current_start,
                    "end_id": current_end,
                    "batch_number": batch_number,
                    "min_id": min_id,
                    "max_id": max_id,
                    "total_range": end_id - start_id,
                    "current_progress": current_start - start_id,
                    "user_count": len(users) if users else 0
                }

                if users:
                    total_processed += len(users)
                    self.logger.debug(f"_id范围 {current_start}-{current_end}: 获取到 {len(users)} 个用户")
                    yield users, batch_info

                    # 检查是否达到总限制
                    if user_limit and total_processed >= user_limit:
                        break
                else:
                    self.logger.debug(f"_id范围 {current_start}-{current_end}: 没有找到用户")
                    # 即使没有用户也要返回批次信息，以便跟踪进度
                    yield [], batch_info

                current_start = current_end

            self.logger.info(f"_id范围查询完成: {total_processed} 个用户")

        except Exception as e:
            self.logger.error(f"按_id范围批量获取用户失败: {e}")
            return

    def _get_users_simple_batch(self, user_limit: Optional[int] = None) -> Iterator[List[Dict[str, Any]]]:
        """
        简单的批次查询（不使用ID范围）

        Args:
            user_limit: 总用户数量限制

        Yields:
            每个批次的用户数据列表
        """
        try:
            self.logger.info("使用简单批次查询模式")

            # 构建查询条件
            query_builder = MongoDBQueryBuilder()

            # 应用日期范围过滤
            self._apply_date_range_filter(query_builder)

            # 应用向量状态过滤
            self._apply_vector_status_filter(query_builder)

            # 应用省份过滤
            self._apply_province_filter(query_builder)

            # 应用通用过滤条件
            self._apply_common_filters(query_builder)

            # 排序（按_id排序以确保一致性）
            query_builder.sort_ascending("_id")

            # 投影字段
            self._apply_projection(query_builder)

            # 分批查询
            batch_size = self.query_config.id_range_span  # 使用相同的批次大小
            skip = 0
            total_processed = 0

            while True:
                # 设置当前批次的限制
                current_limit = batch_size
                if user_limit:
                    remaining_limit = user_limit - total_processed
                    if remaining_limit <= 0:
                        break
                    current_limit = min(batch_size, remaining_limit)

                # 构建查询选项
                query_options = QueryOptions(
                    projection=query_builder.get_projection(),
                    sort=query_builder.get_sort(),
                    limit=current_limit,
                    skip=skip
                )

                # 执行查询
                users = list(self.mongodb_ops.find_many(
                    query_builder.get_filter(),
                    options=query_options
                ))

                if not users:
                    # 没有更多用户
                    break

                total_processed += len(users)
                self.logger.debug(f"简单批次查询: 获取到 {len(users)} 个用户 (跳过: {skip})")
                yield users

                # 检查是否达到总限制
                if user_limit and total_processed >= user_limit:
                    break

                # 如果返回的用户数少于请求的数量，说明已经到达末尾
                if len(users) < current_limit:
                    break

                skip += len(users)

            self.logger.info(f"简单批次查询完成: {total_processed} 个用户")

        except Exception as e:
            self.logger.error(f"简单批次查询失败: {e}")
            return

    def _get_users_simple_batch_with_progress(self, user_limit: Optional[int] = None) -> Iterator[Tuple[List[Dict[str, Any]], Dict[str, Any]]]:
        """
        简单的批次查询（不使用ID范围，带进度信息）

        Args:
            user_limit: 总用户数量限制

        Yields:
            (用户数据列表, 批次信息) 元组
        """
        try:
            self.logger.info("使用简单批次查询模式（带进度信息）")

            # 构建查询条件
            query_builder = MongoDBQueryBuilder()

            # 应用日期范围过滤
            self._apply_date_range_filter(query_builder)

            # 应用向量状态过滤
            self._apply_vector_status_filter(query_builder)

            # 应用省份过滤
            self._apply_province_filter(query_builder)

            # 应用通用过滤条件
            self._apply_common_filters(query_builder)

            # 排序（按_id排序以确保一致性）
            query_builder.sort_ascending("_id")

            # 投影字段
            self._apply_projection(query_builder)

            # 分批查询
            batch_size = self.query_config.id_range_span  # 使用相同的批次大小
            skip = 0
            total_processed = 0
            batch_number = 0

            while True:
                batch_number += 1

                # 设置当前批次的限制
                current_limit = batch_size
                if user_limit:
                    remaining_limit = user_limit - total_processed
                    if remaining_limit <= 0:
                        break
                    current_limit = min(batch_size, remaining_limit)

                # 构建查询选项
                query_options = QueryOptions(
                    projection=query_builder.get_projection(),
                    sort=query_builder.get_sort(),
                    limit=current_limit,
                    skip=skip
                )

                # 执行查询
                users = list(self.mongodb_ops.find_many(
                    query_builder.get_filter(),
                    options=query_options
                ))

                # 构建批次信息
                batch_info = {
                    "start_id": None,  # 简单批次查询没有明确的ID范围
                    "end_id": None,
                    "batch_number": batch_number,
                    "min_id": None,
                    "max_id": None,
                    "total_range": None,
                    "current_progress": skip,
                    "user_count": len(users),
                    "skip": skip,
                    "limit": current_limit
                }

                if not users:
                    # 没有更多用户
                    break

                total_processed += len(users)
                self.logger.debug(f"简单批次查询: 获取到 {len(users)} 个用户 (跳过: {skip})")
                yield users, batch_info

                # 检查是否达到总限制
                if user_limit and total_processed >= user_limit:
                    break

                # 如果返回的用户数少于请求的数量，说明已经到达末尾
                if len(users) < current_limit:
                    break

                skip += len(users)

            self.logger.info(f"简单批次查询完成: {total_processed} 个用户")

        except Exception as e:
            self.logger.error(f"简单批次查询失败: {e}")
            return
    
    def _load_query_config(self):
        """加载查询配置"""
        try:
            # 获取MongoDB配置
            mongodb_config = self.config_manager.get_config("mongodb", default={})
            collections = mongodb_config.get("collections", {})

            # 获取服务配置
            service_config = self.config_manager.get_config("user_vector_service", default={})

            # 新的MongoDB过滤配置
            mongodb_filters = service_config.get("mongodb_filters", {})

            # 日期范围过滤配置
            date_range_config = mongodb_filters.get("date_range", {})

            # 向量状态过滤配置
            vector_status_config = mongodb_filters.get("vector_status", {})

            # 省份过滤配置
            province_config = mongodb_filters.get("province", {})

            # 查询排序配置
            query_sorting = service_config.get("query_sorting", {})

            # ID范围批次处理配置
            id_range_config = service_config.get("id_range_processing", {})

            self.query_config = UserQueryConfig(
                collection_name=collections.get("user_pid_collection", "user_pid_records_optimized"),
                batch_size=self.service_config.user_batch_size,
                max_pids_per_user=service_config.get("max_pids_per_user", 300),
                min_pids_required=self.service_config.min_pids_required,
                # 日期范围过滤配置
                date_range_enabled=date_range_config.get("enabled", False),
                start_date=date_range_config.get("start_date"),
                end_date=date_range_config.get("end_date"),
                recent_days=date_range_config.get("recent_days"),
                # 向量状态过滤配置
                only_not_stored=vector_status_config.get("only_not_stored", True),
                include_expired=vector_status_config.get("include_expired", True),
                # 省份过滤配置
                province_enabled=province_config.get("enabled", False),
                prov_ids=province_config.get("prov_ids", []),
                # 查询排序配置
                sort_by_update_time=query_sorting.get("sort_by_update_time", True),
                sort_direction=query_sorting.get("sort_direction", "desc"),
                # ID范围批次处理配置
                id_range_enabled=id_range_config.get("enabled", True),  # 默认启用
                id_range_span=id_range_config.get("span", 100000),  # 默认100,000个用户一批
                start_id=id_range_config.get("start_id"),  # 可选的起始ID
                end_id=id_range_config.get("end_id")  # 可选的结束ID
            )

        except Exception as e:
            self.logger.error(f"加载查询配置失败: {e}")
            raise

    def get_users_for_processing(self, user_limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        获取需要处理的用户（统一的查询方法）

        Args:
            user_limit: 用户数量限制

        Returns:
            用户数据列表
        """
        try:
            self.logger.debug("查询需要处理的用户")

            # 构建查询条件
            query_builder = MongoDBQueryBuilder()

            # 应用日期范围过滤
            self._apply_date_range_filter(query_builder)

            # 应用向量状态过滤
            self._apply_vector_status_filter(query_builder)

            # 应用省份过滤
            self._apply_province_filter(query_builder)

            # 应用通用过滤条件
            self._apply_common_filters(query_builder)

            # 排序
            if self.query_config.sort_by_update_time:
                if self.query_config.sort_direction == "desc":
                    query_builder.sort_descending("updated_days")
                else:
                    query_builder.sort_ascending("updated_days")

            # 限制数量
            if user_limit:
                query_builder.limit_results(user_limit)

            # 投影字段
            self._apply_projection(query_builder)

            # 执行查询
            # 创建QueryOptions对象
            query_options = QueryOptions(
                projection=query_builder.get_projection() if query_builder.get_projection() else None,
                sort=query_builder.get_sort() if query_builder.get_sort() else None,
                limit=query_builder._limit,
                skip=query_builder._skip,
                hint=query_builder._hint
            )
            
            users = list(self.mongodb_ops.find_many(
                query_builder.get_filter(),
                options=query_options
            ))

            self.logger.info(f"查询到 {len(users)} 个需要处理的用户")
            return users

        except Exception as e:
            self.logger.error(f"查询需要处理的用户失败: {e}")
            return []

    def _apply_date_range_filter(self, query_builder: MongoDBQueryBuilder):
        """应用日期范围过滤"""
        if not self.query_config.date_range_enabled:
            return

        # 如果设置了 recent_days，优先使用
        if self.query_config.recent_days:
            from shared.utils import TimeUtils
            from datetime import datetime

            # 获取今天的日期并转换为datetime对象
            today = TimeUtils.today()
            today_dt = datetime.combine(today, datetime.min.time())

            # 计算开始日期
            start_date_obj = TimeUtils.subtract_days(today, self.query_config.recent_days)
            start_date_dt = datetime.combine(start_date_obj, datetime.min.time())

            # 格式化日期
            end_date = TimeUtils.format_datetime(today_dt, 'compact_date')
            start_date = TimeUtils.format_datetime(start_date_dt, 'compact_date')

            query_builder.date_range("updated_days", start_date, end_date)
        elif self.query_config.start_date and self.query_config.end_date:
            query_builder.date_range("updated_days", self.query_config.start_date, self.query_config.end_date)

    def _apply_vector_status_filter(self, query_builder: MongoDBQueryBuilder):
        """应用向量状态过滤"""
        conditions = []

        if self.query_config.only_not_stored:
            # 条件1: 未存储向量
            conditions.append({"vector_status.is_stored": False})

        if self.query_config.include_expired:
            # 条件2: 向量存储时间早于更新时间（向量过期）
            conditions.append({
                "vector_status.is_stored": True,
                "$expr": {"$lt": ["$vector_status.stored_at_days", "$updated_days"]}
            })

        if conditions:
            if len(conditions) == 1:
                query_builder.filter_dict(conditions[0])
            else:
                query_builder.or_conditions(*conditions)

    def _apply_province_filter(self, query_builder: MongoDBQueryBuilder):
        """应用省份过滤"""
        if self.query_config.province_enabled and self.query_config.prov_ids:
            query_builder.filter_dict({"prov_id": {"$in": self.query_config.prov_ids}})

    def get_users_for_date_range(self, start_date: str, end_date: str, 
                                user_limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        获取日期范围内需要处理的用户
        
        Args:
            start_date: 开始日期 (YYYYMMDD)
            end_date: 结束日期 (YYYYMMDD)
            user_limit: 用户数量限制
            
        Returns:
            用户数据列表
        """
        try:
            self.logger.debug(f"查询日期范围内的用户: {start_date} - {end_date}")
            
            # 构建查询条件
            query_builder = MongoDBQueryBuilder()
            
            # 日期范围过滤
            query_builder.date_range("updated_days", start_date, end_date)

            # 应用通用过滤条件
            self._apply_common_filters(query_builder)

            # 应用向量状态过滤
            self._apply_vector_status_filter(query_builder)

            # 应用省份过滤
            self._apply_province_filter(query_builder)

            # 排序
            if self.query_config.sort_by_update_time:
                query_builder.sort_descending("updated_days")

            # 限制数量
            if user_limit:
                query_builder.limit_results(user_limit)

            # 投影字段
            self._apply_projection(query_builder)
            
            # 执行查询
            # 创建QueryOptions对象
            query_options = QueryOptions(
                projection=query_builder.get_projection() if query_builder.get_projection() else None,
                sort=query_builder.get_sort() if query_builder.get_sort() else None,
                limit=query_builder._limit,
                skip=query_builder._skip,
                hint=query_builder._hint
            )
            
            users = list(self.mongodb_ops.find_many(
                query_builder.get_filter(),
                options=query_options
            ))
            
            self.logger.debug(f"找到 {len(users)} 个用户需要处理")
            return users
            
        except Exception as e:
            self.logger.error(f"查询用户失败: {e}")
            return []
    
    def get_users_by_uids(self, uids: List[int]) -> List[Dict[str, Any]]:
        """
        根据UID列表获取用户数据
        
        Args:
            uids: UID列表
            
        Returns:
            用户数据列表
        """
        try:
            if not uids:
                return []
            
            self.logger.debug(f"根据UID列表查询用户: {len(uids)} 个UID")
            
            # 构建查询条件
            query_builder = MongoDBQueryBuilder()
            query_builder.in_values("_id", uids)  # 使用_id而不是uid

            # 应用通用过滤条件
            self._apply_common_filters(query_builder)

            # 投影字段
            self._apply_projection(query_builder)
            
            # 执行查询
            # 创建QueryOptions对象
            query_options = QueryOptions(
                projection=query_builder.get_projection() if query_builder.get_projection() else None,
                sort=query_builder.get_sort() if query_builder.get_sort() else None,
                limit=query_builder._limit,
                skip=query_builder._skip,
                hint=query_builder._hint
            )
            
            users = list(self.mongodb_ops.find_many(
                query_builder.get_filter(),
                options=query_options
            ))
            
            self.logger.debug(f"找到 {len(users)} 个用户")
            return users
            
        except Exception as e:
            self.logger.error(f"根据UID查询用户失败: {e}")
            return []
    
    def get_users_need_vector_update(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        获取需要更新向量的用户
        
        Args:
            limit: 限制数量
            
        Returns:
            用户数据列表
        """
        try:
            self.logger.debug("查询需要更新向量的用户")
            
            # 构建查询条件
            query_builder = MongoDBQueryBuilder()
            
            # 应用向量状态过滤
            self._apply_vector_status_filter(query_builder)

            # 应用省份过滤
            self._apply_province_filter(query_builder)

            # 应用通用过滤条件
            self._apply_common_filters(query_builder)

            # 排序：优先处理最近更新的用户
            query_builder.sort_descending("updated_days")

            # 限制数量
            if limit:
                query_builder.limit_results(limit)

            # 投影字段
            self._apply_projection(query_builder)
            
            # 执行查询
            # 创建QueryOptions对象
            query_options = QueryOptions(
                projection=query_builder.get_projection() if query_builder.get_projection() else None,
                sort=query_builder.get_sort() if query_builder.get_sort() else None,
                limit=query_builder._limit,
                skip=query_builder._skip,
                hint=query_builder._hint
            )
            
            users = list(self.mongodb_ops.find_many(
                query_builder.get_filter(),
                options=query_options
            ))
            
            self.logger.debug(f"找到 {len(users)} 个需要更新向量的用户")
            return users
            
        except Exception as e:
            self.logger.error(f"查询需要更新向量的用户失败: {e}")
            return []
    
    def get_user_pid_details(self, uid: int) -> Optional[Dict[str, Any]]:
        """
        获取用户的详细PID信息
        
        Args:
            uid: 用户ID
            
        Returns:
            用户详细信息或None
        """
        try:
            # 构建查询条件
            query_builder = MongoDBQueryBuilder()
            query_builder.equals("_id", uid)  # 使用_id而不是uid
            
            # 执行查询
            user_data = self.mongodb_ops.find_one(query_builder.get_filter())
            
            if user_data:
                self.logger.debug(f"获取用户 {uid} 的详细信息成功")
                return user_data
            else:
                self.logger.warning(f"用户 {uid} 不存在")
                return None
            
        except Exception as e:
            self.logger.error(f"获取用户 {uid} 详细信息失败: {e}")
            return None
    
    def get_user_statistics(self) -> Dict[str, Any]:
        """
        获取用户统计信息
        
        Returns:
            统计信息字典
        """
        try:
            self.logger.info("获取用户统计信息")
            
            # 总用户数
            total_users = self.mongodb_ops.count_documents({})
            
            # 已存储向量的用户数
            stored_users = self.mongodb_ops.count_documents({"vector_status.is_stored": True})

            # 未存储向量的用户数
            not_stored_users = self.mongodb_ops.count_documents({"vector_status.is_stored": False})

            # 满足最小PID要求的用户数
            valid_users = self.mongodb_ops.count_documents({
                "pid_count": {"$gte": self.query_config.min_pids_required}
            })

            # 需要更新向量的用户数
            need_update_users = self.mongodb_ops.count_documents({
                "$or": [
                    {"vector_status.is_stored": False},
                    {
                        "vector_status.is_stored": True,
                        "$expr": {"$lt": ["$vector_status.stored_at_days", "$updated_days"]}
                    }
                ],
                "pid_count": {"$gte": self.query_config.min_pids_required}
            })
            
            # 聚合统计
            pipeline = [
                {
                    "$group": {
                        "_id": None,
                        "avg_pid_count": {"$avg": "$pid_count"},
                        "max_pid_count": {"$max": "$pid_count"},
                        "min_pid_count": {"$min": "$pid_count"}
                    }
                }
            ]
            
            agg_result = list(self.mongodb_ops.aggregate(pipeline))
            agg_stats = agg_result[0] if agg_result else {}
            
            stats = {
                "total_users": total_users,
                "stored_users": stored_users,
                "not_stored_users": not_stored_users,
                "valid_users": valid_users,
                "need_update_users": need_update_users,
                "storage_rate": stored_users / total_users if total_users > 0 else 0,
                "avg_pid_count": agg_stats.get("avg_pid_count", 0),
                "max_pid_count": agg_stats.get("max_pid_count", 0),
                "min_pid_count": agg_stats.get("min_pid_count", 0)
            }
            
            self.logger.info(f"用户统计信息: {stats}")
            return stats
            
        except Exception as e:
            self.logger.error(f"获取用户统计信息失败: {e}")
            return {}
    
    def get_recent_users(self, days: int = 7, limit: int = 1000) -> List[Dict[str, Any]]:
        """
        获取最近更新的用户
        
        Args:
            days: 最近天数
            limit: 限制数量
            
        Returns:
            用户数据列表
        """
        try:
            self.logger.info(f"获取最近 {days} 天更新的用户")
            
            # 计算日期范围
            current_days = TimeUtils.date_to_days(TimeUtils.today())
            start_days = current_days - days
            
            # 构建查询条件
            query_builder = MongoDBQueryBuilder()
            query_builder.greater_than_or_equal("updated_days", start_days)

            # 应用通用过滤条件
            self._apply_common_filters(query_builder)

            # 排序
            query_builder.sort_descending("updated_days")

            # 限制数量
            query_builder.limit_results(limit)

            # 投影字段
            self._apply_projection(query_builder)
            
            # 执行查询
            # 创建QueryOptions对象
            query_options = QueryOptions(
                projection=query_builder.get_projection() if query_builder.get_projection() else None,
                sort=query_builder.get_sort() if query_builder.get_sort() else None,
                limit=query_builder._limit,
                skip=query_builder._skip,
                hint=query_builder._hint
            )
            
            users = list(self.mongodb_ops.find_many(
                query_builder.get_filter(),
                options=query_options
            ))
            
            self.logger.info(f"找到 {len(users)} 个最近更新的用户")
            return users
            
        except Exception as e:
            self.logger.error(f"获取最近更新用户失败: {e}")
            return []
    
    def validate_user_data(self, user_data: Dict[str, Any]) -> bool:
        """
        验证用户数据
        
        Args:
            user_data: 用户数据
            
        Returns:
            验证结果
        """
        try:
            # 检查必需字段
            required_fields = ["_id", "pid_groups", "pid_count"]
            for field in required_fields:
                if field not in user_data:
                    self.logger.warning(f"用户数据缺少必需字段: {field}")
                    return False

            # 验证UID（现在是_id字段）
            uid = user_data["_id"]
            if not DataValidator.validate(uid, 'uid', 'uid'):
                return False

            # 验证PID组列表
            pid_groups = user_data["pid_groups"]
            if not isinstance(pid_groups, list):
                self.logger.warning(f"用户 {uid} 的pid_groups不是列表类型")
                return False

            # 计算总PID数量
            total_pids = sum(len(group.get("pids", [])) for group in pid_groups)
            pid_count = user_data["pid_count"]
            if total_pids != pid_count:
                self.logger.warning(f"用户 {uid} 的PID数量不匹配: {total_pids} != {pid_count}")
                return False

            # 检查最小PID要求
            if pid_count < self.query_config.min_pids_required:
                self.logger.debug(f"用户 {uid} 的PID数量不足: {pid_count} < {self.query_config.min_pids_required}")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"验证用户数据失败: {e}")
            return False
