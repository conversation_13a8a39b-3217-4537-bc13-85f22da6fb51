#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预计算PCA模型加载器

负责加载和管理预计算的PCA模型：
- 加载预计算的PCA模型
- 提供向量降维功能
- 模型验证和状态监控
- 线程安全的模型访问

作者: User-DF Team
版本: 2.0.0
"""

import os
import pickle
import threading
from typing import List, Optional, Dict, Any
from pathlib import Path
import numpy as np
from sklearn.decomposition import PCA

from shared.core import Logger, ConfigManager
from shared.utils import TimeUtils


class PrecomputedPCALoader:
    """预计算PCA模型加载器"""
    
    def __init__(self, config_manager: ConfigManager, config: Any):
        """
        初始化PCA模型加载器
        
        Args:
            config_manager: 配置管理器
            config: 服务配置
        """
        self.config_manager = config_manager
        self.config = config
        self.logger = Logger.get_logger("PrecomputedPCALoader")
        
        # 模型相关
        self.pca_model: Optional[PCA] = None
        self.model_metadata: Optional[Dict[str, Any]] = None
        self.model_lock = threading.RLock()
        
        # 加载配置
        self._load_config()
        
        # 加载模型
        self._load_model()
        
        self.logger.info("预计算PCA模型加载器初始化完成")
    
    def _load_config(self):
        """加载配置"""
        try:
            # 获取PCA配置
            pca_config = getattr(self.config, 'pca_config', {})
            
            # 模型路径配置
            self.model_dir = Path("models/pca_precomputed")
            self.model_path = self.model_dir / "latest_pca_model.pkl"
            
            # 如果配置中指定了模型路径，使用配置的路径
            if hasattr(self.config, 'precomputed_pca_model_path'):
                self.model_path = Path(self.config.precomputed_pca_model_path)
            
            # 维度配置
            self.source_dimension = getattr(self.config, 'source_dimension', 512)
            self.target_dimension = getattr(self.config, 'target_dimension', 256)
            
            self.logger.info(f"PCA模型路径: {self.model_path}")
            
        except Exception as e:
            self.logger.error(f"加载PCA配置失败: {e}")
            raise
    
    def _load_model(self):
        """加载PCA模型"""
        try:
            if not self.model_path.exists():
                self.logger.warning(f"PCA模型文件不存在: {self.model_path}")
                self.logger.info("请先运行 pca_precompute.py 生成预计算模型")
                return
            
            with self.model_lock:
                # 加载模型数据
                with open(self.model_path, 'rb') as f:
                    model_data = pickle.load(f)
                
                # 验证模型数据格式
                if not isinstance(model_data, dict) or 'pca_model' not in model_data:
                    raise ValueError("无效的模型文件格式")
                
                self.pca_model = model_data['pca_model']
                self.model_metadata = {
                    'source_dimension': model_data.get('source_dimension', self.source_dimension),
                    'target_dimension': model_data.get('target_dimension', self.target_dimension),
                    'training_timestamp': model_data.get('training_timestamp', 'unknown'),
                    'validation_results': model_data.get('validation_results', {}),
                    'model_version': model_data.get('model_version', 'unknown'),
                    'loaded_at': TimeUtils.format_datetime(TimeUtils.now(), 'datetime')
                }
                
                # 验证模型维度
                if (self.model_metadata['source_dimension'] != self.source_dimension or 
                    self.model_metadata['target_dimension'] != self.target_dimension):
                    self.logger.warning(
                        f"模型维度与配置不匹配: "
                        f"模型({self.model_metadata['source_dimension']}->{self.model_metadata['target_dimension']}) "
                        f"vs 配置({self.source_dimension}->{self.target_dimension})"
                    )
                
                # 测试模型
                self._test_model()
                
                self.logger.info(f"PCA模型加载成功")
                self.logger.info(f"模型信息: {self.model_metadata['source_dimension']}->{self.model_metadata['target_dimension']}")
                self.logger.info(f"训练时间: {self.model_metadata['training_timestamp']}")
                
                # 输出验证结果
                validation_results = self.model_metadata.get('validation_results', {})
                if validation_results:
                    self.logger.info(f"模型验证结果:")
                    self.logger.info(f"  解释方差比例: {validation_results.get('explained_variance_ratio', 'N/A'):.4f}")
                    self.logger.info(f"  相似度相关系数: {validation_results.get('similarity_correlation', 'N/A'):.4f}")
                    self.logger.info(f"  压缩比: {validation_results.get('compression_ratio', 'N/A'):.2f}x")
                
        except Exception as e:
            self.logger.error(f"加载PCA模型失败: {e}")
            self.pca_model = None
            self.model_metadata = None
    
    def _test_model(self):
        """测试模型功能"""
        try:
            if self.pca_model is None:
                raise ValueError("模型未加载")
            
            # 生成测试向量
            test_vector = np.random.randn(1, self.source_dimension).astype(np.float32)
            
            # 测试降维
            reduced_vector = self.pca_model.transform(test_vector)
            
            # 验证输出维度
            if reduced_vector.shape[1] != self.target_dimension:
                raise ValueError(f"降维结果维度错误: {reduced_vector.shape[1]} != {self.target_dimension}")
            
            self.logger.debug("PCA模型测试通过")
            
        except Exception as e:
            self.logger.error(f"PCA模型测试失败: {e}")
            raise
    
    def transform_vector(self, vector: List[float]) -> Optional[List[float]]:
        """
        使用预计算PCA模型转换向量
        
        Args:
            vector: 输入向量（512维）
            
        Returns:
            转换后的向量（256维）或None
        """
        try:
            if self.pca_model is None:
                self.logger.warning("PCA模型未加载，无法进行向量转换")
                return None
            
            # 验证输入维度
            if len(vector) != self.source_dimension:
                self.logger.warning(f"输入向量维度不匹配: {len(vector)} != {self.source_dimension}")
                return None
            
            with self.model_lock:
                # 转换向量
                vector_array = np.array([vector], dtype=np.float32)
                reduced_vector = self.pca_model.transform(vector_array)
                
                return reduced_vector[0].tolist()
            
        except Exception as e:
            self.logger.error(f"PCA向量转换失败: {e}")
            return None
    
    def transform_vectors_batch(self, vectors: List[List[float]]) -> List[Optional[List[float]]]:
        """
        批量转换向量
        
        Args:
            vectors: 输入向量列表
            
        Returns:
            转换后的向量列表
        """
        try:
            if self.pca_model is None:
                self.logger.warning("PCA模型未加载，无法进行批量向量转换")
                return [None] * len(vectors)
            
            if not vectors:
                return []
            
            # 验证输入维度
            valid_vectors = []
            valid_indices = []
            
            for i, vector in enumerate(vectors):
                if len(vector) == self.source_dimension:
                    valid_vectors.append(vector)
                    valid_indices.append(i)
                else:
                    self.logger.debug(f"跳过维度不匹配的向量 {i}: {len(vector)} != {self.source_dimension}")
            
            if not valid_vectors:
                self.logger.warning("没有有效的输入向量")
                return [None] * len(vectors)
            
            with self.model_lock:
                # 批量转换
                vectors_array = np.array(valid_vectors, dtype=np.float32)
                reduced_vectors = self.pca_model.transform(vectors_array)
                
                # 构建结果列表
                results = [None] * len(vectors)
                for i, valid_idx in enumerate(valid_indices):
                    results[valid_idx] = reduced_vectors[i].tolist()
                
                return results
            
        except Exception as e:
            self.logger.error(f"批量PCA向量转换失败: {e}")
            return [None] * len(vectors)
    
    def is_model_loaded(self) -> bool:
        """检查模型是否已加载"""
        return self.pca_model is not None
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        if self.model_metadata is None:
            return {
                "loaded": False,
                "error": "模型未加载"
            }
        
        return {
            "loaded": True,
            "source_dimension": self.model_metadata['source_dimension'],
            "target_dimension": self.model_metadata['target_dimension'],
            "training_timestamp": self.model_metadata['training_timestamp'],
            "model_version": self.model_metadata['model_version'],
            "loaded_at": self.model_metadata['loaded_at'],
            "validation_results": self.model_metadata.get('validation_results', {})
        }
    
    def reload_model(self) -> bool:
        """重新加载模型"""
        try:
            self.logger.info("重新加载PCA模型")
            self._load_model()
            return self.pca_model is not None
        except Exception as e:
            self.logger.error(f"重新加载PCA模型失败: {e}")
            return False
