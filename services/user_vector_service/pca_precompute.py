#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PCA模型预计算模块

独立的PCA模型预计算功能：
- 从Milvus采样100万个512维内容向量
- 训练PCA模型将512维降至256维
- 使用验证集评估降维效果
- 保存训练好的PCA模型

作者: User-DF Team
版本: 2.0.0
"""

import os
import sys
import pickle
import argparse
import numpy as np
from typing import List, Tuple, Dict, Any, Optional
from pathlib import Path
from sklearn.decomposition import PCA
from sklearn.metrics.pairwise import cosine_similarity
import time
import random

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from shared.core import Logger, ConfigManager, ExceptionHandler
from shared.database.milvus import MilvusPool, MilvusVectorOperations
from shared.utils import TimeUtils


class PCAPrecompute:
    """PCA预计算器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化PCA预计算器
        
        Args:
            config_path: 配置文件路径，默认使用development.yaml
        """
        self.logger = Logger.get_logger("PCAPrecompute")
        
        # 加载配置
        if config_path:
            # 使用指定的配置文件
            self.config_manager = ConfigManager.create_from_config_file(config_path)
        else:
            # 默认使用统一配置目录中的user_vector_service配置
            self.config_manager = ConfigManager(config_dir=str(project_root / "configs"))
            try:
                # 加载user_vector_service配置
                self.config_manager.load_config("user_vector_service")
            except FileNotFoundError:
                raise Exception(f"找不到配置文件，请确保存在 configs/user_vector_service/development.yaml")
        
        # 获取配置
        self.milvus_config = self.config_manager.get_config("milvus")
        self.service_config = self.config_manager.get_config("user_vector_service", default={})
        
        # PCA配置
        self.source_dimension = 512
        self.target_dimension = 256
        self.sample_size = 1000000  # 100万样本
        self.validation_size = 10000  # 1万验证样本
        self.batch_size = 10000  # 批处理大小
        
        # 集合配置
        collections = self.milvus_config.get("collections", {})
        self.content_collection = collections.get("content_collection", "content_tower_collection_20250616")
        
        # 模型保存路径
        self.model_save_dir = Path("models/pca_precomputed")
        self.model_save_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化Milvus连接
        self.milvus_pool = None
        self.vector_ops = None
        self.use_mock_data = False

        # 检查是否强制使用模拟数据
        if os.getenv('USE_MOCK_DATA', 'false').lower() == 'true':
            self.use_mock_data = True
            self.logger.info("环境变量设置使用模拟数据")
        else:
            self._init_milvus()

        self.logger.info("PCA预计算器初始化完成")
    
    def _init_milvus(self):
        """初始化Milvus连接"""
        try:
            self.milvus_pool = MilvusPool("milvus", config_manager=self.config_manager)

            # 快速测试连接
            client_info = self.milvus_pool.get_client()
            if client_info is None:
                raise Exception("无法获取Milvus客户端")

            # 初始化向量操作类
            self.vector_ops = MilvusVectorOperations(self.milvus_pool, self.content_collection)

            self.logger.info("Milvus连接池和向量操作类初始化成功")
        except Exception as e:
            self.logger.warning(f"Milvus连接池初始化失败: {e}")
            self.logger.info("将使用模拟数据进行PCA训练")
            self.milvus_pool = None
            self.vector_ops = None
            self.use_mock_data = True

    def _generate_mock_vectors(self, sample_size: int) -> Tuple[np.ndarray, List[str]]:
        """
        生成模拟向量数据用于测试

        Args:
            sample_size: 采样数量

        Returns:
            (向量数组, ID列表)
        """
        self.logger.info(f"生成 {sample_size} 个模拟向量数据")

        # 设置随机种子以确保可重现性
        np.random.seed(42)

        # 生成具有一定结构的随机向量
        # 使用多个高斯分布的混合来模拟真实的向量分布
        n_clusters = 10
        cluster_size = sample_size // n_clusters

        vectors = []
        ids = []

        for cluster_id in range(n_clusters):
            # 为每个聚类生成一个随机中心
            cluster_center = np.random.randn(self.source_dimension) * 0.5

            # 生成该聚类的向量
            cluster_vectors = np.random.randn(cluster_size, self.source_dimension) * 0.3 + cluster_center

            # L2标准化
            norms = np.linalg.norm(cluster_vectors, axis=1, keepdims=True)
            cluster_vectors = cluster_vectors / (norms + 1e-8)

            vectors.append(cluster_vectors)

            # 生成对应的ID
            cluster_ids = [f"mock_item_{cluster_id}_{i}" for i in range(cluster_size)]
            ids.extend(cluster_ids)

        # 处理剩余的向量
        remaining = sample_size - len(ids)
        if remaining > 0:
            extra_vectors = np.random.randn(remaining, self.source_dimension) * 0.5
            norms = np.linalg.norm(extra_vectors, axis=1, keepdims=True)
            extra_vectors = extra_vectors / (norms + 1e-8)
            vectors.append(extra_vectors)

            extra_ids = [f"mock_item_extra_{i}" for i in range(remaining)]
            ids.extend(extra_ids)

        # 合并所有向量
        all_vectors = np.vstack(vectors).astype(np.float32)

        # 随机打乱
        indices = np.random.permutation(len(all_vectors))
        all_vectors = all_vectors[indices]
        ids = [ids[i] for i in indices]

        self.logger.info(f"生成模拟数据完成: {len(all_vectors)} 个 {self.source_dimension} 维向量")

        return all_vectors, ids

    def sample_vectors_from_milvus(self, sample_size: int) -> Tuple[np.ndarray, List[str]]:
        """
        从Milvus采样向量数据

        Args:
            sample_size: 采样数量

        Returns:
            (向量数组, ID列表)
        """
        try:
            # 如果使用模拟数据
            if self.use_mock_data or self.vector_ops is None:
                return self._generate_mock_vectors(sample_size)

            self.logger.info(f"开始从集合 {self.content_collection} 采样 {sample_size} 个向量")

            # 使用新的query_all_vectors方法进行大数据集查询
            # 这个方法会自动处理Milvus的16384查询窗口限制
            result = self.vector_ops.query_all_vectors(
                filter_expr="",  # 使用空过滤器获取所有数据
                output_fields=["item_id", "item_embedding"],
                batch_size=self.batch_size,
                max_results=sample_size  # 限制最大结果数量
            )

            if not result.success:
                raise Exception(f"查询向量数据失败: {result.error_message}")

            if not result.results:
                raise Exception("未能查询到任何向量数据")

            # 处理查询结果
            sampled_vectors = []
            sampled_ids = []

            for record in result.results:
                item_id = record.get("item_id")
                embedding = record.get("item_embedding")

                if embedding and len(embedding) == self.source_dimension:
                    sampled_vectors.append(embedding)
                    sampled_ids.append(str(item_id))

            if len(sampled_vectors) == 0:
                raise Exception("未能采样到任何有效的向量数据")

            # 如果采样数据超过需要的数量，随机选择
            if len(sampled_vectors) > sample_size:
                indices = np.random.choice(len(sampled_vectors), size=sample_size, replace=False)
                sampled_vectors = [sampled_vectors[i] for i in indices]
                sampled_ids = [sampled_ids[i] for i in indices]

            vectors_array = np.array(sampled_vectors, dtype=np.float32)

            self.logger.info(f"采样完成，获得 {len(sampled_vectors)} 个 {self.source_dimension} 维向量")
            self.logger.info(f"查询执行时间: {result.execution_time:.3f}秒")
            self.logger.info(f"有效向量比例: {len(sampled_vectors)}/{len(result.results)} ({len(sampled_vectors)/len(result.results)*100:.1f}%)")

            return vectors_array, sampled_ids

        except Exception as e:
            self.logger.error(f"从Milvus采样向量失败: {e}")
            raise
    
    def train_pca_model(self, training_vectors: np.ndarray) -> PCA:
        """
        训练PCA模型
        
        Args:
            training_vectors: 训练向量数组
            
        Returns:
            训练好的PCA模型
        """
        try:
            self.logger.info(f"开始训练PCA模型，输入维度: {self.source_dimension}, 目标维度: {self.target_dimension}")
            self.logger.info(f"训练样本数: {len(training_vectors)}")
            
            # 创建PCA模型
            pca_model = PCA(
                n_components=self.target_dimension,
                random_state=42,
                svd_solver='auto'
            )
            
            # 训练模型
            start_time = time.time()
            pca_model.fit(training_vectors)
            training_time = time.time() - start_time
            
            # 输出训练结果
            explained_variance_ratio = np.sum(pca_model.explained_variance_ratio_)
            self.logger.info(f"PCA模型训练完成，耗时: {training_time:.2f}秒")
            self.logger.info(f"解释方差比例: {explained_variance_ratio:.4f}")
            self.logger.info(f"各主成分解释方差比例前10个: {pca_model.explained_variance_ratio_[:10]}")
            
            return pca_model
            
        except Exception as e:
            self.logger.error(f"训练PCA模型失败: {e}")
            raise

    def validate_pca_model(self, pca_model: PCA, validation_vectors: np.ndarray) -> Dict[str, float]:
        """
        验证PCA模型效果

        Args:
            pca_model: 训练好的PCA模型
            validation_vectors: 验证向量数组

        Returns:
            验证结果字典
        """
        try:
            self.logger.info(f"开始验证PCA模型，验证样本数: {len(validation_vectors)}")

            # 降维
            reduced_vectors = pca_model.transform(validation_vectors)

            # 重构
            reconstructed_vectors = pca_model.inverse_transform(reduced_vectors)

            # 计算重构误差
            mse = np.mean((validation_vectors - reconstructed_vectors) ** 2)
            rmse = np.sqrt(mse)

            # 计算余弦相似度保持率
            similarities_original = []
            similarities_reduced = []

            # 随机选择1000对向量进行相似度比较
            n_pairs = min(1000, len(validation_vectors) // 2)
            indices = np.random.choice(len(validation_vectors), size=n_pairs * 2, replace=False)

            for i in range(0, len(indices), 2):
                idx1, idx2 = indices[i], indices[i + 1]

                # 原始向量相似度
                sim_orig = cosine_similarity(
                    validation_vectors[idx1:idx1+1],
                    validation_vectors[idx2:idx2+1]
                )[0, 0]

                # 降维向量相似度
                sim_reduced = cosine_similarity(
                    reduced_vectors[idx1:idx1+1],
                    reduced_vectors[idx2:idx2+1]
                )[0, 0]

                similarities_original.append(sim_orig)
                similarities_reduced.append(sim_reduced)

            # 计算相似度保持率
            similarity_correlation = np.corrcoef(similarities_original, similarities_reduced)[0, 1]
            similarity_mae = np.mean(np.abs(np.array(similarities_original) - np.array(similarities_reduced)))

            # 计算压缩比
            compression_ratio = self.source_dimension / self.target_dimension

            validation_results = {
                "mse": float(mse),
                "rmse": float(rmse),
                "similarity_correlation": float(similarity_correlation),
                "similarity_mae": float(similarity_mae),
                "compression_ratio": float(compression_ratio),
                "explained_variance_ratio": float(np.sum(pca_model.explained_variance_ratio_)),
                "validation_samples": len(validation_vectors)
            }

            self.logger.info("PCA模型验证结果:")
            self.logger.info(f"  重构均方误差(MSE): {mse:.6f}")
            self.logger.info(f"  重构均方根误差(RMSE): {rmse:.6f}")
            self.logger.info(f"  相似度相关系数: {similarity_correlation:.4f}")
            self.logger.info(f"  相似度平均绝对误差: {similarity_mae:.4f}")
            self.logger.info(f"  压缩比: {compression_ratio:.2f}x")
            self.logger.info(f"  解释方差比例: {validation_results['explained_variance_ratio']:.4f}")

            return validation_results

        except Exception as e:
            self.logger.error(f"验证PCA模型失败: {e}")
            raise

    def save_pca_model(self, pca_model: PCA, validation_results: Dict[str, float]) -> str:
        """
        保存PCA模型

        Args:
            pca_model: 训练好的PCA模型
            validation_results: 验证结果

        Returns:
            保存的模型文件路径
        """
        try:
            # 生成模型文件名
            timestamp = TimeUtils.format_datetime(TimeUtils.now(), 'compact')
            model_filename = f"pca_model_{self.source_dimension}to{self.target_dimension}_{timestamp}.pkl"
            model_path = self.model_save_dir / model_filename

            # 准备保存的数据
            model_data = {
                "pca_model": pca_model,
                "source_dimension": self.source_dimension,
                "target_dimension": self.target_dimension,
                "training_timestamp": timestamp,
                "validation_results": validation_results,
                "model_version": "2.0.0"
            }

            # 保存模型
            with open(model_path, 'wb') as f:
                pickle.dump(model_data, f)

            # 创建符号链接到最新模型
            latest_model_path = self.model_save_dir / "latest_pca_model.pkl"
            if latest_model_path.exists():
                latest_model_path.unlink()
            latest_model_path.symlink_to(model_filename)

            # 保存验证报告
            report_path = self.model_save_dir / f"validation_report_{timestamp}.txt"
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(f"PCA模型验证报告\n")
                f.write(f"生成时间: {TimeUtils.format_datetime(TimeUtils.now(), 'datetime')}\n")
                f.write(f"模型文件: {model_filename}\n")
                f.write(f"源维度: {self.source_dimension}\n")
                f.write(f"目标维度: {self.target_dimension}\n")
                f.write(f"训练样本数: {self.sample_size}\n")
                f.write(f"验证样本数: {validation_results['validation_samples']}\n")
                f.write(f"\n验证结果:\n")
                for key, value in validation_results.items():
                    f.write(f"  {key}: {value}\n")

            self.logger.info(f"PCA模型保存成功: {model_path}")
            self.logger.info(f"验证报告保存: {report_path}")

            return str(model_path)

        except Exception as e:
            self.logger.error(f"保存PCA模型失败: {e}")
            raise

    def run_precompute(self) -> str:
        """
        运行完整的PCA预计算流程

        Returns:
            保存的模型文件路径
        """
        try:
            self.logger.info("开始PCA预计算流程")

            # 1. 采样训练数据
            self.logger.info("步骤1: 采样训练数据")
            total_sample_size = self.sample_size + self.validation_size
            all_vectors, _ = self.sample_vectors_from_milvus(total_sample_size)

            if len(all_vectors) < self.validation_size:
                raise Exception(f"采样数据不足，需要至少 {self.validation_size} 个样本")

            # 2. 分割训练集和验证集
            self.logger.info("步骤2: 分割训练集和验证集")
            indices = np.random.permutation(len(all_vectors))

            validation_indices = indices[:self.validation_size]
            training_indices = indices[self.validation_size:]

            validation_vectors = all_vectors[validation_indices]
            training_vectors = all_vectors[training_indices]

            self.logger.info(f"训练集大小: {len(training_vectors)}")
            self.logger.info(f"验证集大小: {len(validation_vectors)}")

            # 3. 训练PCA模型
            self.logger.info("步骤3: 训练PCA模型")
            pca_model = self.train_pca_model(training_vectors)

            # 4. 验证模型效果
            self.logger.info("步骤4: 验证模型效果")
            validation_results = self.validate_pca_model(pca_model, validation_vectors)

            # 5. 保存模型
            self.logger.info("步骤5: 保存模型")
            model_path = self.save_pca_model(pca_model, validation_results)

            self.logger.info("PCA预计算流程完成")
            return model_path

        except Exception as e:
            self.logger.error(f"PCA预计算流程失败: {e}")
            raise

    def load_and_test_model(self, model_path: str) -> bool:
        """
        加载并测试保存的模型

        Args:
            model_path: 模型文件路径

        Returns:
            测试是否成功
        """
        try:
            self.logger.info(f"加载并测试模型: {model_path}")

            # 加载模型
            with open(model_path, 'rb') as f:
                model_data = pickle.load(f)

            pca_model = model_data["pca_model"]

            # 生成测试向量
            test_vector = np.random.randn(1, self.source_dimension).astype(np.float32)

            # 测试降维
            reduced_vector = pca_model.transform(test_vector)

            # 验证维度
            if reduced_vector.shape[1] != self.target_dimension:
                raise Exception(f"降维结果维度错误: {reduced_vector.shape[1]} != {self.target_dimension}")

            self.logger.info("模型测试成功")
            self.logger.info(f"模型信息: {model_data.get('model_version', 'unknown')}")
            self.logger.info(f"训练时间: {model_data.get('training_timestamp', 'unknown')}")

            return True

        except Exception as e:
            self.logger.error(f"模型测试失败: {e}")
            return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="PCA模型预计算工具")
    parser.add_argument("--config", type=str, help="配置文件路径")
    parser.add_argument("--sample-size", type=int, default=1000000, help="训练样本数量")
    parser.add_argument("--validation-size", type=int, default=10000, help="验证样本数量")
    parser.add_argument("--target-dim", type=int, default=256, help="目标维度")
    parser.add_argument("--test-model", type=str, help="测试指定的模型文件")
    parser.add_argument("--dry-run", action="store_true", help="仅测试连接，不执行训练")

    args = parser.parse_args()

    try:
        # 创建预计算器
        precomputer = PCAPrecompute(args.config)

        # 更新配置参数
        if args.sample_size:
            precomputer.sample_size = args.sample_size
        if args.validation_size:
            precomputer.validation_size = args.validation_size
        if args.target_dim:
            precomputer.target_dimension = args.target_dim

        # 测试模型模式
        if args.test_model:
            success = precomputer.load_and_test_model(args.test_model)
            sys.exit(0 if success else 1)

        # 干运行模式
        if args.dry_run:
            precomputer.logger.info("干运行模式：测试Milvus连接")
            if precomputer.vector_ops is None:
                precomputer.logger.error("Milvus连接或向量操作初始化失败")
                sys.exit(1)
            else:
                precomputer.logger.info("Milvus连接和向量操作正常")
                sys.exit(0)

        # 运行预计算
        model_path = precomputer.run_precompute()

        # 测试保存的模型
        if precomputer.load_and_test_model(model_path):
            precomputer.logger.info("PCA预计算完成并验证成功")
            print(f"模型保存路径: {model_path}")
        else:
            precomputer.logger.error("模型验证失败")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
