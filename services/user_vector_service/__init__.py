"""
用户向量服务 - 重构版

重构版本的用户向量计算和存储服务，使用共享模块架构：
- 统一配置管理
- 统一日志管理
- 统一数据库连接
- 统一异常处理
- 增量PCA训练
- 批量向量处理

主要功能：
- 从MongoDB获取用户历史访问记录
- 从Milvus获取内容向量
- 计算用户向量（非监督降维）
- 存储用户向量到Milvus
"""

from .main import main
from .service import UserVectorService
from .processors import VectorProcessor, PCAModelManager
from .mongodb_query import UserDataQuery
from .milvus_operations import VectorOperations

__all__ = [
    "main",
    "UserVectorService",
    "VectorProcessor",
    "PCAModelManager", 
    "UserDataQuery",
    "VectorOperations"
]
