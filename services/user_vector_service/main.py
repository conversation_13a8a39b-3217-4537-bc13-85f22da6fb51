#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户向量服务主入口

重构版本的用户向量计算和存储服务主程序：
- 命令行参数解析
- 服务初始化和启动
- 信号处理和优雅关闭
- 健康检查和监控

作者: User-DF Team
版本: 2.0.0
"""

import sys
import os
import argparse
import signal
import time
from typing import Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from shared.core import ConfigManager, Logger, ExceptionHandler
from services.user_vector_service.service import UserVectorService


class ServiceRunner:
    """服务运行器"""

    def __init__(self):
        """初始化服务运行器"""
        self.service: Optional[UserVectorService] = None
        self.logger: Optional[Logger] = None
        self.shutdown_requested = False
        self.signal_count = 0

        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, _frame):
        """信号处理器"""
        self.signal_count += 1

        if self.signal_count == 1:
            # 第一次信号：优雅关闭
            if self.logger:
                self.logger.info(f"接收到信号 {signum}，准备关闭服务...")
                self.logger.info("正在停止当前任务，请稍候... (再次按 Ctrl+C 强制退出)")
            self.shutdown_requested = True
        elif self.signal_count == 2:
            # 第二次信号：强制退出
            if self.logger:
                self.logger.warning("接收到第二次中断信号，强制退出服务")
            else:
                print("强制退出服务...")
            sys.exit(1)
        else:
            # 第三次及以上信号：立即退出
            print("立即退出...")
            os._exit(1)
    
    def _merge_config_with_args(self, config_manager, args):
        """
        合并配置文件参数和命令行参数

        Args:
            config_manager: 配置管理器
            args: 命令行参数

        Returns:
            合并后的参数对象
        """
        # 获取服务配置
        service_config = config_manager.get_config('user_vector_service', default={})

        # 简化的处理模式配置
        default_mode = service_config.get('default_processing_mode', 'process_users')

        # 日志配置
        logging_config = service_config.get('logging', {})

        # 如果没有指定任何处理模式，使用默认模式
        if not any([args.health_check, args.process_users]):
            if default_mode == 'health_check':
                args.health_check = True
            else:  # 默认为 process_users
                args.process_users = True

        # 合并用户限制参数
        if not args.user_limit:
            args.user_limit = service_config.get('user_limit')

        # 合并日志配置
        if not hasattr(args, 'log_level') or args.log_level == 'INFO':
            args.log_level = logging_config.get('level', 'INFO')

        if not args.verbose:
            args.verbose = logging_config.get('verbose', False)

        return args

    def _get_processing_mode_name(self, args):
        """获取当前处理模式名称"""
        if args.health_check:
            return "健康检查"
        elif args.process_users:
            return "用户向量处理"
        else:
            return "未知模式"

    def run(self, args):
        """
        运行服务

        Args:
            args: 命令行参数
        """
        try:
            # 根据参数初始化配置管理器
            if args.config:
                # 使用指定的配置文件
                config_manager = ConfigManager.create_from_config_file(args.config)
                self.logger = Logger.get_logger("UserVectorServiceRunner")
                self.logger.info(f"使用配置文件: {args.config}")
            else:
                # 使用默认的环境配置方式
                project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
                config_dir = os.path.join(project_root, 'configs')
                config_manager = ConfigManager(config_dir=config_dir)
                self.logger = Logger.get_logger("UserVectorServiceRunner")
                self.logger.info(f"使用默认环境配置: development")

            # 合并配置文件参数和命令行参数
            args = self._merge_config_with_args(config_manager, args)

            # 验证合并后的参数
            validate_arguments(args)

            # 初始化日志
            self.logger = Logger.get_logger("UserVectorServiceRunner")
            self.logger.info("=== 用户向量服务启动 ===")
            self.logger.info(f"处理模式: {self._get_processing_mode_name(args)}")

            # 初始化服务
            self.service = UserVectorService(config_manager)

            # 将shutdown_requested标志传递给服务
            self.service.set_shutdown_callback(lambda: self.shutdown_requested)
            
            # 执行健康检查
            if args.health_check:
                self._perform_health_check()
                return
            
            # 根据参数执行不同的处理模式
            if args.health_check:
                # 执行健康检查
                self._perform_health_check()
                return
            elif args.process_users:
                # 处理用户向量
                success = self._process_users(args.user_limit)
            else:
                # 默认执行用户向量处理
                self.logger.info("未指定处理模式，执行用户向量处理")
                success = self._process_users(args.user_limit)
            
            # 输出结果
            if success:
                self.logger.info("=== 服务执行成功 ===")
            else:
                self.logger.error("=== 服务执行失败 ===")
                sys.exit(1)
                
        except KeyboardInterrupt:
            self.logger.info("用户中断服务")
        except Exception as e:
            if self.logger:
                self.logger.error(f"服务运行失败: {e}")
            ExceptionHandler.handle_exception(e)
            sys.exit(1)
        finally:
            if self.logger:
                self.logger.info("=== 用户向量服务结束 ===")
    
    def _perform_health_check(self):
        """执行健康检查"""
        try:
            health_status = self.service.health_check()
            
            self.logger.info("=== 健康检查结果 ===")
            self.logger.info(f"服务状态: {health_status.get('status', 'unknown')}")
            
            # MongoDB健康状态
            mongodb_health = health_status.get('mongodb', {})
            self.logger.info(f"MongoDB状态: {mongodb_health.get('status', 'unknown')}")
            
            # Milvus健康状态
            milvus_health = health_status.get('milvus', {})
            self.logger.info(f"Milvus状态: {milvus_health.get('status', 'unknown')}")
            
            # PCA模型状态
            pca_health = health_status.get('pca_model', {})
            self.logger.info(f"PCA模型状态: 已加载={pca_health.get('is_loaded', False)}")

            # 统计信息
            stats = health_status.get('stats', {})
            self.logger.info(f"已处理用户: {stats.get('processed_users', 0)}")
            self.logger.info(f"计算向量: {stats.get('vectors_computed', 0)}")
            self.logger.info(f"存储向量: {stats.get('vectors_stored', 0)}")
            
            # 判断整体健康状态
            overall_status = health_status.get('status', 'unknown')
            if overall_status == 'healthy':
                self.logger.info("✅ 服务健康检查通过")
            else:
                self.logger.error("❌ 服务健康检查失败")
                sys.exit(1)
                
        except Exception as e:
            self.logger.error(f"健康检查失败: {e}")
            sys.exit(1)
    
    def _process_users(self, user_limit: Optional[int]) -> bool:
        """处理用户向量（统一的用户处理方法）"""
        try:
            self.logger.info("开始处理用户向量")

            # 调用服务的统一用户处理方法
            success = self.service.process_users(user_limit)

            return success

        except Exception as e:
            self.logger.error(f"处理用户向量失败: {e}")
            return False


def create_argument_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="用户向量服务 - 简化版",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 使用默认配置运行（默认处理用户向量）
  python -m services.user_vector_service.main

  # 显式处理用户向量
  python -m services.user_vector_service.main --process-users

  # 健康检查
  python -m services.user_vector_service.main --health-check

  # 指定配置文件
  python -m services.user_vector_service.main --config configs/user_vector_service/production.yaml

  # 限制处理用户数量
  python -m services.user_vector_service.main --user-limit 1000
        """
    )

    # 处理模式参数组（简化为两种模式）
    mode_group = parser.add_mutually_exclusive_group(required=False)
    mode_group.add_argument(
        '--health-check',
        action='store_true',
        help='执行健康检查'
    )
    mode_group.add_argument(
        '--process-users',
        action='store_true',
        help='处理用户向量（根据配置文件中的过滤条件）'
    )
    
    # 限制参数
    parser.add_argument(
        '--user-limit',
        type=int,
        help='限制处理的用户数量'
    )

    # 配置文件路径
    parser.add_argument(
        '--config',
        type=str,
        help='指定配置文件路径（如果不指定，将使用默认的 development 环境配置）'
    )
    
    # 日志级别
    parser.add_argument(
        '--log-level',
        type=str,
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
        default='INFO',
        help='日志级别 (默认: INFO)'
    )
    
    # 详细输出
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='启用详细输出'
    )
    
    return parser


def validate_arguments(args):
    """验证命令行参数"""
    # 验证用户限制参数
    if args.user_limit and args.user_limit <= 0:
        raise ValueError("用户限制数量必须大于0")


def main():
    """主函数"""
    try:
        # 解析命令行参数
        parser = create_argument_parser()
        args = parser.parse_args()

        # 根据参数设置配置管理器
        if hasattr(args, 'config') and args.config:
            # 使用指定的配置文件
            print(f"使用配置文件: {args.config}")
        else:
            # 使用默认的环境配置方式
            print(f"使用默认环境配置: development")

        # 创建并运行服务（参数验证将在服务内部进行，合并配置后）
        runner = ServiceRunner()
        runner.run(args)

    except Exception as e:
        print(f"启动失败: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
