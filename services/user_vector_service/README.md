# 用户向量服务 - 重构版

## 概述

重构版本的用户向量计算和存储服务，使用新的共享模块架构，提供更好的可维护性、可扩展性和性能。

## 主要特性

### 🔄 架构重构
- **共享模块架构**: 使用统一的配置、日志、数据库连接管理
- **模块化设计**: 清晰的职责分离和组件解耦
- **统一异常处理**: 标准化的错误处理和重试机制

### 🧮 向量计算
- **预计算PCA模型**: 使用预训练的PCA模型进行向量降维
- **多种聚合策略**: 平均值、加权平均、最大池化等
- **时间权重**: 基于时间的权重计算，越新的访问记录权重越大
- **向量验证**: 严格的向量数据验证和清洗

### 🗄️ 数据库操作
- **MongoDB查询**: 高效的用户数据查询和过滤
- **Milvus向量操作**: 批量向量存储和搜索
- **连接池管理**: 数据库连接池复用和健康检查
- **批量操作**: 优化的批量处理减少网络开销

### ⚡ 性能优化
- **单进程批处理**: 高效的单进程批量处理用户数据
- **批量处理**: 用户批次和向量批次的双重优化
- **内存管理**: 智能内存使用和垃圾回收
- **预计算模型**: 使用预训练PCA模型避免重复计算

### 📈 监控和日志
- **实时监控**: 处理进度和性能指标监控
- **详细日志**: 结构化日志和错误追踪
- **健康检查**: 服务和依赖组件健康状态检查
- **统计报告**: 详细的处理统计和性能报告

## 目录结构

```
services/user_vector_service/
├── __init__.py              # 模块初始化
├── main.py                  # 服务主入口
├── service.py               # 主服务类
├── processors.py            # 向量处理器和PCA模型管理器
├── mongodb_query.py         # MongoDB用户数据查询
├── milvus_operations.py     # Milvus向量操作
├── test_service.py          # 测试脚本
└── README.md               # 说明文档
```

## 配置文件

服务配置位于 `configs/user_vector_service/` 目录，支持多环境配置：

- `development.yaml`: 开发环境配置
- `production.yaml`: 生产环境配置

### 配置文件功能特性

- **默认处理模式**: 可配置服务在不指定命令行参数时的默认行为
- **参数覆盖**: 命令行参数可覆盖配置文件中的设置
- **环境特定配置**: 不同环境使用不同的默认配置
- **完整配置管理**: 所有运行时参数都可通过配置文件设置

### 主要配置项

- **处理模式配置**: 默认处理模式（健康检查或用户处理）
- **MongoDB过滤配置**: 日期范围、向量状态、省份过滤等查询条件
- **查询排序配置**: 排序字段和方向设置
- **批处理配置**: 批次大小、超时设置
- **向量计算配置**: PID要求、聚合方法、权重策略
- **PCA配置**: 预计算模型路径、模型配置参数
- **监控配置**: 进度报告、统计输出、性能监控
- **日志配置**: 日志级别、输出格式、详细程度
- **环境配置**: 环境名称、配置目录路径

## 使用方法

### 安装依赖

```bash
# 安装项目依赖
pip install -r requirements.txt

# 或使用项目安装
pip install -e .
```

### 基本使用

#### 使用默认配置（推荐）

```bash
# 使用默认配置运行（默认处理用户向量，使用 development 环境配置）
python -m services.user_vector_service.main

# 使用指定的配置文件
python -m services.user_vector_service.main --config configs/user_vector_service/production.yaml
```

#### 使用命令行参数（覆盖配置文件设置）

```bash
# 处理用户向量（根据配置文件中的过滤条件）
python -m services.user_vector_service.main --process-users

# 健康检查
python -m services.user_vector_service.main --health-check

# 限制处理用户数量
python -m services.user_vector_service.main --user-limit 1000

# 使用生产环境配置文件
python -m services.user_vector_service.main --config configs/user_vector_service/production.yaml
```

### 新功能特性 (v2.1.0 - 简化版)

#### 简化的处理模式
- 只保留两种处理模式：健康检查和用户向量处理
- 移除了复杂的多种处理模式，简化了使用方式
- 所有过滤逻辑统一到MongoDB查询参数中

#### 统一的MongoDB过滤配置
- 日期范围过滤：支持绝对日期范围或相对天数过滤
- 向量状态过滤：只处理未存储向量或向量过期的用户
- 省份过滤：支持按`prov_id`字段过滤特定省份的用户
- 查询排序：可配置的排序字段和方向

#### 配置文件驱动
- 所有过滤条件都通过配置文件管理
- 支持不同环境的不同默认配置
- 命令行参数可覆盖配置文件设置

#### 乐观锁更新机制
- 使用`updated_days`字段作为乐观锁
- 只有当用户数据未被其他进程修改时才更新向量状态
- 支持乱序批量更新，提高并发性能

#### PCA降维处理
- 自动使用预计算的PCA模型进行512D→256D降维
- 支持向量聚合和权重计算
- 时间衰减权重策略优化用户向量质量

### 环境配置

```bash
# 指定运行环境
python -m services.user_vector_service.main --date-range 20240101,20240107 --environment production

# 指定配置目录
python -m services.user_vector_service.main --date-range 20240101,20240107 --config-dir /path/to/configs

# 设置日志级别
python -m services.user_vector_service.main --date-range 20240101,20240107 --log-level DEBUG
```

### 测试服务

```bash
# 运行测试脚本
python services/user_vector_service/test_service.py
```

## 数据流程

1. **查询用户数据**: 从MongoDB获取需要处理的用户
2. **获取内容向量**: 从Milvus获取用户PID对应的内容向量
3. **计算用户向量**: 使用聚合策略计算用户向量
4. **向量降维**: 使用预计算PCA模型对用户向量降维（可选）
5. **存储用户向量**: 将用户向量存储到Milvus
6. **更新状态**: 更新MongoDB中的向量存储状态

## 数据格式

### 输入数据 (MongoDB用户数据)
```json
{
  "uid": 12345,
  "pid_list": ["pid_1", "pid_2", "pid_3"],
  "pid_count": 3,
  "updated_days": 19000,
  "is_stored": false,
  "stored_at_days": null,
  "pid_timestamps": {
    "19000": ["pid_1", "pid_2"],
    "19001": ["pid_3"]
  }
}
```

### 输出数据 (Milvus用户向量)
```json
{
  "uid": 12345,
  "user_vector": [0.1, 0.2, 0.3, ...],  // 256维向量
}
```

## 性能特性

### 处理能力
- **吞吐量**: 支持每秒处理数百个用户
- **批处理**: 高效的单进程批量处理
- **内存**: 优化的内存使用，支持大规模用户处理
- **扩展**: 支持水平扩展和分布式部署

### 优化策略
- **批量操作**: 减少数据库交互次数
- **连接池**: 复用数据库连接
- **预计算模型**: 使用预训练PCA模型提高效率
- **内存管理**: 智能垃圾回收和内存释放

## 监控和告警

### 监控指标
- 处理速度（用户/秒）
- 向量计算成功率
- PCA模型状态
- 内存使用率
- 数据库连接状态

### 日志级别
- **DEBUG**: 详细的调试信息
- **INFO**: 一般信息和进度报告
- **WARNING**: 警告信息和可恢复错误
- **ERROR**: 错误信息和异常
- **CRITICAL**: 严重错误和系统故障

## 故障排除

### 常见问题

1. **MongoDB查询失败**
   - 检查MongoDB服务状态
   - 验证连接配置
   - 检查用户数据格式

2. **Milvus连接失败**
   - 检查Milvus服务状态
   - 验证集合配置
   - 检查网络连接

3. **PCA模型加载失败**
   - 检查预计算模型文件路径
   - 验证模型文件完整性
   - 检查向量维度匹配

4. **向量计算错误**
   - 检查内容向量质量
   - 验证聚合参数
   - 检查PID数据

### 调试方法

```bash
# 启用详细日志
python -m services.user_vector_service.main --date-range 20240101,20240107 --log-level DEBUG --verbose

# 健康检查
python -m services.user_vector_service.main --health-check

# 运行测试
python services/user_vector_service/test_service.py
```

## 版本历史

### v2.0.0 (重构版)
- 使用共享模块架构
- 统一配置和日志管理
- 优化向量计算性能
- 使用预计算PCA模型
- 单进程批处理模式

### v1.0.0 (原版)
- 基础用户向量计算功能
- 简单的PCA训练
- 单进程处理模式

## 贡献指南

1. 遵循项目的代码规范
2. 添加适当的测试用例
3. 更新相关文档
4. 提交前运行测试脚本

## 许可证

本项目采用 MIT 许可证。
