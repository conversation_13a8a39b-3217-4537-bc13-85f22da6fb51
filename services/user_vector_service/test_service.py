#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户向量服务测试脚本

用于测试重构后的用户向量服务功能：
- 配置加载测试
- 组件初始化测试
- 向量计算测试
- PCA模型测试
- MongoDB和Milvus连接测试

作者: User-DF Team
版本: 2.0.0
"""

import sys
import os
import tempfile
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from shared.core import ConfigManager, Logger
from .service import UserVectorService
from .processors import VectorProcessor, PCAModelManager


def test_config_loading():
    """测试配置加载"""
    print("\n=== 测试配置加载 ===")
    
    try:
        config_manager = ConfigManager()
        
        # 测试加载各种配置
        global_config = config_manager.get_config("global")
        mongodb_config = config_manager.get_config("mongodb")
        milvus_config = config_manager.get_config("milvus")
        service_config = config_manager.get_config("user_vector_service")
        
        print("✅ 配置加载成功")
        print(f"  - 全局配置: {len(global_config)} 项")
        print(f"  - MongoDB配置: {len(mongodb_config)} 项")
        print(f"  - Milvus配置: {len(milvus_config)} 项")
        print(f"  - 服务配置: {len(service_config)} 项")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False


def test_service_initialization():
    """测试服务初始化"""
    print("\n=== 测试服务初始化 ===")
    
    try:
        config_manager = ConfigManager()
        service = UserVectorService(config_manager)
        
        print("✅ 服务初始化成功")
        print(f"  - 配置: {service.config}")
        print(f"  - 用户批次大小: {service.config.user_batch_size}")
        print(f"  - 向量批次大小: {service.config.vector_batch_size}")
        print(f"  - PCA方法: {service.config.pca_method}")
        
        return True, service
        
    except Exception as e:
        print(f"❌ 服务初始化失败: {e}")
        return False, None


def test_vector_processor():
    """测试向量处理器"""
    print("\n=== 测试向量处理器 ===")
    
    try:
        config_manager = ConfigManager()
        service = UserVectorService(config_manager)
        
        # 创建测试向量数据
        content_vectors = [
            np.random.rand(512).tolist() for _ in range(10)
        ]
        timestamps = list(range(19000, 19010))
        
        # 计算用户向量
        user_vector = service.vector_processor.compute_user_vector(
            content_vectors, timestamps
        )
        
        print("✅ 向量处理测试成功")
        print(f"  - 输入向量数: {len(content_vectors)}")
        print(f"  - 输出向量维度: {len(user_vector) if user_vector else 0}")
        print(f"  - 聚合方法: {service.vector_processor.aggregation_method}")
        print(f"  - 权重策略: {service.vector_processor.weight_strategy}")
        
        return True
        
    except Exception as e:
        print(f"❌ 向量处理测试失败: {e}")
        return False


def test_pca_model_manager():
    """测试PCA模型管理器"""
    print("\n=== 测试PCA模型管理器 ===")
    
    try:
        config_manager = ConfigManager()
        service = UserVectorService(config_manager)
        
        # 创建测试数据
        test_vectors = [
            np.random.rand(512).tolist() for _ in range(100)
        ]
        
        # 测试增量训练
        if service.config.pca_method == "incremental_pca":
            success = service.pca_manager.train_incremental_batch(test_vectors)
            print(f"  - 增量训练: {'成功' if success else '失败'}")
        
        # 测试向量转换
        test_vector = np.random.rand(512).tolist()
        transformed = service.pca_manager.transform_vector(test_vector)
        
        # 获取模型状态
        model_status = service.pca_manager.get_model_status()
        
        print("✅ PCA模型管理器测试成功")
        print(f"  - 模型已加载: {model_status.is_loaded}")
        print(f"  - 目标维度: {model_status.n_components}")
        print(f"  - 训练样本数: {model_status.n_samples_seen}")
        print(f"  - 向量转换: {'成功' if transformed else '失败'}")
        
        return True
        
    except Exception as e:
        print(f"❌ PCA模型管理器测试失败: {e}")
        return False


def test_mongodb_connection():
    """测试MongoDB连接"""
    print("\n=== 测试MongoDB连接 ===")
    
    try:
        config_manager = ConfigManager()
        service = UserVectorService(config_manager)
        
        # 测试用户统计查询
        stats = service.user_data_query.get_user_statistics()
        
        print("✅ MongoDB连接测试成功")
        print(f"  - 总用户数: {stats.get('total_users', 0)}")
        print(f"  - 已存储向量用户数: {stats.get('stored_users', 0)}")
        print(f"  - 需要更新用户数: {stats.get('need_update_users', 0)}")
        print(f"  - 平均PID数: {stats.get('avg_pid_count', 0):.1f}")
        
        return True
        
    except Exception as e:
        print(f"❌ MongoDB连接测试失败: {e}")
        return False


def test_milvus_connection():
    """测试Milvus连接"""
    print("\n=== 测试Milvus连接 ===")
    
    try:
        config_manager = ConfigManager()
        service = UserVectorService(config_manager)
        
        # 执行健康检查
        health_status = service.health_check()
        
        milvus_status = health_status.get('milvus', {}).get('status', 'unknown')
        
        if milvus_status == 'healthy':
            print("✅ Milvus连接成功")
            print(f"  - 连接状态: {milvus_status}")
            
            # 获取集合统计
            collection_stats = service.vector_operations.get_collection_stats()
            if collection_stats:
                content_collection = collection_stats.get('content_collection', {})
                user_collection = collection_stats.get('user_collection', {})
                
                print(f"  - 内容集合: {content_collection.get('name', 'unknown')}")
                print(f"    实体数: {content_collection.get('num_entities', 0)}")
                print(f"    维度: {content_collection.get('dimension', 0)}")
                
                print(f"  - 用户集合: {user_collection.get('name', 'unknown')}")
                print(f"    实体数: {user_collection.get('num_entities', 0)}")
                print(f"    维度: {user_collection.get('dimension', 0)}")
        else:
            print(f"❌ Milvus连接失败: {milvus_status}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Milvus连接测试失败: {e}")
        return False


def test_user_data_query():
    """测试用户数据查询"""
    print("\n=== 测试用户数据查询 ===")
    
    try:
        config_manager = ConfigManager()
        service = UserVectorService(config_manager)
        
        # 测试获取最近用户
        recent_users = service.user_data_query.get_recent_users(days=7, limit=10)
        
        # 测试获取需要更新的用户
        need_update_users = service.user_data_query.get_users_need_vector_update(limit=5)
        
        print("✅ 用户数据查询测试成功")
        print(f"  - 最近7天用户数: {len(recent_users)}")
        print(f"  - 需要更新用户数: {len(need_update_users)}")
        
        # 显示示例用户数据
        if recent_users:
            user = recent_users[0]
            print(f"  - 示例用户: UID={user.get('uid')}, PID数={user.get('pid_count', 0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 用户数据查询测试失败: {e}")
        return False


def test_vector_operations():
    """测试向量操作"""
    print("\n=== 测试向量操作 ===")
    
    try:
        config_manager = ConfigManager()
        service = UserVectorService(config_manager)
        
        # 创建测试用户向量
        test_user_vectors = [
            (12345, np.random.rand(256).tolist()),
            (12346, np.random.rand(256).tolist())
        ]
        
        # 测试存储用户向量（如果Milvus可用）
        try:
            store_success = service.vector_operations.store_user_vectors(test_user_vectors)
            print(f"  - 向量存储测试: {'成功' if store_success else '失败'}")
        except Exception as e:
            print(f"  - 向量存储测试: 跳过 ({e})")
        
        # 测试向量搜索
        try:
            query_vector = np.random.rand(256).tolist()
            similar_users = service.vector_operations.search_similar_users(query_vector, top_k=5)
            print(f"  - 向量搜索测试: 找到 {len(similar_users)} 个相似用户")
        except Exception as e:
            print(f"  - 向量搜索测试: 跳过 ({e})")
        
        print("✅ 向量操作测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 向量操作测试失败: {e}")
        return False


def test_batch_processing():
    """测试批量处理"""
    print("\n=== 测试批量处理 ===")
    
    try:
        config_manager = ConfigManager()
        service = UserVectorService(config_manager)
        
        # 获取少量测试用户
        test_users = service.user_data_query.get_recent_users(days=1, limit=3)
        
        if test_users:
            # 测试批量处理
            success = service.process_user_batch(test_users)
            
            print("✅ 批量处理测试成功")
            print(f"  - 测试用户数: {len(test_users)}")
            print(f"  - 处理结果: {'成功' if success else '失败'}")
        else:
            print("⚠️  没有测试用户，跳过批量处理测试")
        
        return True
        
    except Exception as e:
        print(f"❌ 批量处理测试失败: {e}")
        return False


def run_all_tests():
    """运行所有测试"""
    print("开始运行用户向量服务测试...")
    
    tests = [
        ("配置加载", test_config_loading),
        ("服务初始化", test_service_initialization),
        ("向量处理器", test_vector_processor),
        ("PCA模型管理器", test_pca_model_manager),
        ("MongoDB连接", test_mongodb_connection),
        ("Milvus连接", test_milvus_connection),
        ("用户数据查询", test_user_data_query),
        ("向量操作", test_vector_operations),
        ("批量处理", test_batch_processing),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"测试失败: {test_name}")
        except Exception as e:
            print(f"测试异常: {test_name}, {e}")
    
    print(f"\n=== 测试总结 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return True
    else:
        print("⚠️  部分测试失败")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
