#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Milvus向量操作模块

专门处理用户向量服务的Milvus操作：
- 获取内容向量
- 存储用户向量
- 批量向量操作
- 向量查询和验证

作者: User-DF Team
版本: 2.0.0
"""

from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import numpy as np

from shared.core import Lo<PERSON>, ConfigManager, ExceptionHandler
from shared.database.milvus import MilvusPool, MilvusVectorOperations, MilvusCollectionManager
from shared.utils import DataProcessor


@dataclass
class VectorConfig:
    """向量配置"""
    content_collection: str
    user_collection: str
    content_vector_dim: int
    user_vector_dim: int
    batch_size: int
    search_params: Dict[str, Any]


class VectorOperations:
    """Milvus向量操作"""
    
    def __init__(self, milvus_pool: Milvus<PERSON>ool, 
                 config_manager: ConfigManager,
                 service_config: Any):
        """
        初始化Milvus向量操作
        
        Args:
            milvus_pool: Milvus连接池
            config_manager: 配置管理器
            service_config: 服务配置
        """
        self.milvus_pool = milvus_pool
        self.config_manager = config_manager
        self.service_config = service_config
        self.logger = Logger.get_logger("VectorOperations")

        # 关闭回调函数
        self.shutdown_callback = None

        # 加载向量配置
        self._load_vector_config()
        
        # 初始化Milvus操作
        self.content_ops = MilvusVectorOperations(
            self.milvus_pool, 
            self.vector_config.content_collection
        )
        self.user_ops = MilvusVectorOperations(
            self.milvus_pool,
            self.vector_config.user_collection
        )
        self.collection_manager = MilvusCollectionManager(self.milvus_pool)
        
        # 数据处理器
        self.data_processor = DataProcessor()
        
        # 确保用户向量集合存在
        self._ensure_user_collection_exists()
        
        self.logger.info("Milvus向量操作初始化完成")

    def set_shutdown_callback(self, callback):
        """
        设置关闭回调函数

        Args:
            callback: 返回是否应该关闭的回调函数
        """
        self.shutdown_callback = callback

    def should_shutdown(self) -> bool:
        """
        检查是否应该关闭服务

        Returns:
            是否应该关闭
        """
        if self.shutdown_callback:
            return self.shutdown_callback()
        return False

    def _load_vector_config(self):
        """加载向量配置"""
        try:
            # 获取Milvus配置
            milvus_config = self.config_manager.get_config("milvus", default={})
            collections = milvus_config.get("collections", {})
            vector_dims = milvus_config.get("vector_dimensions", {})
            
            # 获取服务配置
            service_config = self.config_manager.get_config("user_vector_service", default={})
            
            self.vector_config = VectorConfig(
                content_collection=collections.get("content_collection", "content_tower_collection"),
                user_collection=collections.get("user_collection", "user_tower_collection"),
                content_vector_dim=vector_dims.get("content_vector_dim", 512),
                user_vector_dim=vector_dims.get("user_vector_dim", 256),
                batch_size=self.service_config.vector_batch_size,
                search_params=service_config.get("search_params", {"nprobe": 10})
            )
            
        except Exception as e:
            self.logger.error(f"加载向量配置失败: {e}")
            raise
    
    def _ensure_user_collection_exists(self):
        """确保用户向量集合存在"""
        try:
            if not self.collection_manager.collection_exists(self.vector_config.user_collection):
                self.logger.info(f"创建用户向量集合: {self.vector_config.user_collection}")
                
                from shared.database.milvus.collection_manager import CollectionSchema
                schema = CollectionSchema(
                    collection_name=self.vector_config.user_collection,
                    dimension=self.vector_config.user_vector_dim,
                    description="用户向量集合",
                    metric_type="COSINE",
                    index_type="IVF_FLAT",
                    auto_id=False,
                    primary_field="user_id",
                    vector_field="user_embedding"
                )
                
                self.collection_manager.create_collection(schema)
                self.logger.info(f"用户向量集合创建成功: {self.vector_config.user_collection}")
            
        except Exception as e:
            self.logger.error(f"确保用户向量集合存在失败: {e}")
            raise
    
    def get_content_vectors_for_users(self, users: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        为用户获取内容向量（批量优化版本）

        Args:
            users: 用户数据列表

        Returns:
            包含内容向量的用户数据列表
        """
        try:
            self.logger.info(f"为 {len(users)} 个用户获取内容向量")

            if not users:
                return []

            # 1. 收集所有用户的PID数据
            user_pid_data = []
            all_pids = set()

            for user in users:
                # 检查是否需要关闭服务
                if self.should_shutdown():
                    self.logger.info("接收到关闭信号，停止获取内容向量")
                    break

                # 从新的文档结构中提取数据
                uid = user["_id"]  # 使用_id字段
                pid_groups = user.get("pid_groups", [])

                # 从pid_groups中提取PID列表和时间戳
                pid_list, pid_timestamps = self._extract_pids_from_groups(pid_groups)

                if not pid_list:
                    self.logger.debug(f"用户 {uid} 没有有效的PID")
                    continue

                # 保存用户的PID数据
                user_pid_data.append({
                    "uid": uid,
                    "pid_list": pid_list,
                    "pid_timestamps": pid_timestamps,
                    "original_pid_count": len(pid_list)
                })

                # 收集所有PID用于批量查询
                all_pids.update(pid_list)

            if not user_pid_data:
                self.logger.info("没有用户有有效的PID")
                return []

            # 2. 批量查询所有PID的向量数据
            self.logger.info(f"批量查询 {len(all_pids)} 个唯一PID的向量数据")
            pid_vector_map = self._batch_query_pid_vectors(list(all_pids))

            if not pid_vector_map:
                self.logger.warning("批量查询PID向量失败")
                return []

            # 3. 为每个用户分配对应的向量数据
            result = []
            for user_data in user_pid_data:
                # 检查是否需要关闭服务
                if self.should_shutdown():
                    self.logger.info("接收到关闭信号，停止向量分配")
                    break

                uid = user_data["uid"]
                pid_list = user_data["pid_list"]
                pid_timestamps = user_data["pid_timestamps"]

                # 从批量查询结果中提取该用户的向量
                content_vectors = []
                timestamps = []

                for pid in pid_list:
                    if pid in pid_vector_map:
                        vector = pid_vector_map[pid]
                        if vector and len(vector) == self.vector_config.content_vector_dim:
                            content_vectors.append(vector)
                            # 获取时间戳
                            timestamp = self._get_timestamp_for_pid(pid, pid_timestamps)
                            timestamps.append(timestamp)

                if content_vectors:
                    result.append({
                        "uid": uid,
                        "content_vectors": content_vectors,
                        "timestamps": timestamps,
                        "original_pid_count": user_data["original_pid_count"],
                        "valid_vector_count": len(content_vectors)
                    })

                    self.logger.debug(f"用户 {uid}: {len(content_vectors)}/{user_data['original_pid_count']} 个有效向量")

            self.logger.info(f"成功获取 {len(result)} 个用户的内容向量，查询了 {len(pid_vector_map)} 个有效PID")
            return result

        except Exception as e:
            self.logger.error(f"获取用户内容向量失败: {e}")
            return []

    def _batch_query_pid_vectors(self, pid_list: List[str]) -> Dict[str, List[float]]:
        """
        批量查询PID对应的向量数据

        Args:
            pid_list: PID列表

        Returns:
            PID到向量的映射字典
        """
        try:
            if not pid_list:
                return {}

            pid_vector_map = {}
            batch_size = self.vector_config.batch_size

            self.logger.debug(f"开始批量查询 {len(pid_list)} 个PID的向量，批次大小: {batch_size}")

            for i in range(0, len(pid_list), batch_size):
                # 检查是否需要关闭服务
                if self.should_shutdown():
                    self.logger.info("接收到关闭信号，停止批量PID向量查询")
                    break

                batch_pids = pid_list[i:i + batch_size]

                # 查询向量
                query_result = self.content_ops.query_vectors(
                    ids=batch_pids,
                    output_fields=["item_embedding"]  # 使用正确的字段名
                )

                if query_result.success and query_result.results:
                    for result in query_result.results:
                        pid = result.get("item_id")  # 使用正确的ID字段名
                        vector = result.get("item_embedding")  # 使用正确的向量字段名

                        if pid and vector and len(vector) == self.vector_config.content_vector_dim:
                            pid_vector_map[str(pid)] = vector
                        elif pid:
                            self.logger.debug(f"PID {pid} 的向量无效或维度不匹配")
                else:
                    self.logger.warning(f"批次查询失败: {len(batch_pids)} 个PID")

            self.logger.debug(f"批量查询完成，成功获取 {len(pid_vector_map)}/{len(pid_list)} 个PID的向量")
            return pid_vector_map

        except Exception as e:
            self.logger.error(f"批量查询PID向量失败: {e}")
            return {}

    def _extract_pids_from_groups(self, pid_groups: List[Dict[str, Any]]) -> Tuple[List[str], Dict[str, List[str]]]:
        """
        从pid_groups中提取PID列表和时间戳映射

        Args:
            pid_groups: PID组列表，格式为 [{"timestamp_days": 19000, "pids": ["pid1", "pid2"]}, ...]

        Returns:
            (PID列表, 时间戳映射字典)
        """
        try:
            all_pids = []
            pid_timestamps = {}

            for group in pid_groups:
                timestamp_days = group.get("timestamp_days")
                pids = group.get("pids", [])

                if timestamp_days is not None and pids:
                    # 添加到总PID列表
                    all_pids.extend(pids)

                    # 构建时间戳映射（以字符串形式存储时间戳）
                    timestamp_str = str(timestamp_days)
                    pid_timestamps[timestamp_str] = pids

            # 去重PID列表（保持顺序）
            seen = set()
            unique_pids = []
            for pid in all_pids:
                if pid not in seen:
                    seen.add(pid)
                    unique_pids.append(pid)

            return unique_pids, pid_timestamps

        except Exception as e:
            self.logger.error(f"从pid_groups提取PID失败: {e}")
            return [], {}
    
    # 注意：_get_content_vectors_for_pids 方法已被弃用，使用 _batch_query_pid_vectors 替代
    
    def _get_timestamp_for_pid(self, pid: str, pid_timestamps: Dict[str, Any]) -> int:
        """
        获取PID的时间戳
        
        Args:
            pid: PID
            pid_timestamps: 时间戳映射
            
        Returns:
            时间戳（天数格式）
        """
        try:
            # 如果有分组的时间戳数据
            if isinstance(pid_timestamps, dict):
                for timestamp_str, pids in pid_timestamps.items():
                    if isinstance(pids, list) and pid in pids:
                        return int(timestamp_str)
            
            # 默认使用当前时间
            from shared.utils import TimeUtils
            return TimeUtils.date_to_days(TimeUtils.today())
            
        except Exception as e:
            self.logger.debug(f"获取PID {pid} 时间戳失败: {e}")
            from shared.utils import TimeUtils
            return TimeUtils.date_to_days(TimeUtils.today())
    
    def store_user_vectors(self, user_vectors: List[Tuple[int, List[float]]]) -> bool:
        """
        存储用户向量到Milvus
        
        Args:
            user_vectors: 用户向量列表 [(uid, vector), ...]
            
        Returns:
            存储是否成功
        """
        try:
            if not user_vectors:
                return True
            
            self.logger.info(f"存储 {len(user_vectors)} 个用户向量到Milvus")
            
            # 准备数据
            data_to_insert = []
            for uid, vector in user_vectors:
                # 验证向量维度
                if len(vector) != self.vector_config.user_vector_dim:
                    self.logger.warning(f"用户 {uid} 向量维度不匹配: {len(vector)} != {self.vector_config.user_vector_dim}")
                    continue
                
                # 验证向量值
                if not self._validate_vector(vector):
                    self.logger.warning(f"用户 {uid} 向量包含无效值")
                    continue
                
                data_to_insert.append({
                    "user_id": uid,
                    "user_embedding": vector
                })
            
            if not data_to_insert:
                self.logger.warning("没有有效的用户向量可存储")
                return False
            
            # 批量插入
            batch_size = self.vector_config.batch_size
            total_inserted = 0
            
            for i in range(0, len(data_to_insert), batch_size):
                # 检查是否需要关闭服务
                if self.should_shutdown():
                    self.logger.info("接收到关闭信号，停止向量存储")
                    return False

                batch_data = data_to_insert[i:i + batch_size]

                # 使用upsert操作
                result = self.user_ops.upsert_vectors(batch_data)

                if result.success:
                    total_inserted += len(batch_data)
                    self.logger.debug(f"批次存储成功: {len(batch_data)} 个向量")
                else:
                    self.logger.error(f"批次存储失败: {result.error_message}")
                    return False
            
            self.logger.info(f"用户向量存储完成: {total_inserted}/{len(user_vectors)} 个向量")
            return total_inserted == len(user_vectors)
            
        except Exception as e:
            self.logger.error(f"存储用户向量失败: {e}")
            return False
    
    def _validate_vector(self, vector: List[float]) -> bool:
        """
        验证向量值
        
        Args:
            vector: 向量
            
        Returns:
            验证结果
        """
        try:
            vector_array = np.array(vector)
            
            # 检查NaN和Inf
            if np.any(np.isnan(vector_array)) or np.any(np.isinf(vector_array)):
                return False
            
            # 检查向量范数
            norm = np.linalg.norm(vector_array)
            if norm == 0:
                return False
            
            return True
            
        except Exception as e:
            self.logger.debug(f"向量验证失败: {e}")
            return False
    
    def search_similar_users(self, query_vector: List[float], 
                           top_k: int = 10) -> List[Dict[str, Any]]:
        """
        搜索相似用户
        
        Args:
            query_vector: 查询向量
            top_k: 返回数量
            
        Returns:
            相似用户列表
        """
        try:
            if len(query_vector) != self.vector_config.user_vector_dim:
                self.logger.error(f"查询向量维度不匹配: {len(query_vector)} != {self.vector_config.user_vector_dim}")
                return []
            
            # 执行向量搜索
            search_result = self.user_ops.search_vectors(
                query_vectors=[query_vector],
                limit=top_k,
                search_params=self.vector_config.search_params,
                output_fields=["user_id"]
            )

            if search_result.success and search_result.results:
                similar_users = []
                for result in search_result.results:
                    similar_users.append({
                        "uid": result.get("user_id"),
                        "distance": result.get("distance"),
                        "similarity": 1.0 - result.get("distance", 1.0)  # 转换为相似度
                    })
                
                self.logger.info(f"找到 {len(similar_users)} 个相似用户")
                return similar_users
            else:
                self.logger.warning(f"向量搜索失败: {search_result.error_message}")
                return []
            
        except Exception as e:
            self.logger.error(f"搜索相似用户失败: {e}")
            return []
    
    def get_user_vector(self, uid: int) -> Optional[List[float]]:
        """
        获取用户向量
        
        Args:
            uid: 用户ID
            
        Returns:
            用户向量或None
        """
        try:
            query_result = self.user_ops.query_vectors(
                ids=[uid],
                output_fields=["user_embedding"]
            )

            if query_result.success and query_result.results:
                result = query_result.results[0]
                return result.get("user_embedding")
            else:
                self.logger.debug(f"用户 {uid} 的向量不存在")
                return None
            
        except Exception as e:
            self.logger.error(f"获取用户 {uid} 向量失败: {e}")
            return None
    
    def delete_user_vectors(self, uids: List[int]) -> bool:
        """
        删除用户向量
        
        Args:
            uids: 用户ID列表
            
        Returns:
            删除是否成功
        """
        try:
            if not uids:
                return True
            
            self.logger.info(f"删除 {len(uids)} 个用户向量")
            
            # 批量删除
            batch_size = self.vector_config.batch_size
            total_deleted = 0
            
            for i in range(0, len(uids), batch_size):
                batch_uids = uids[i:i + batch_size]
                
                result = self.user_ops.delete_vectors(batch_uids)
                
                if result.success:
                    total_deleted += len(batch_uids)
                    self.logger.debug(f"批次删除成功: {len(batch_uids)} 个向量")
                else:
                    self.logger.error(f"批次删除失败: {result.error_message}")
                    return False
            
            self.logger.info(f"用户向量删除完成: {total_deleted} 个向量")
            return True
            
        except Exception as e:
            self.logger.error(f"删除用户向量失败: {e}")
            return False
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """
        获取集合统计信息
        
        Returns:
            统计信息字典
        """
        try:
            # 获取内容集合信息
            content_info = self.collection_manager.get_collection_info(
                self.vector_config.content_collection
            )
            
            # 获取用户集合信息
            user_info = self.collection_manager.get_collection_info(
                self.vector_config.user_collection
            )
            
            return {
                "content_collection": {
                    "name": content_info.name,
                    "num_entities": content_info.num_entities,
                    "dimension": content_info.dimension
                },
                "user_collection": {
                    "name": user_info.name,
                    "num_entities": user_info.num_entities,
                    "dimension": user_info.dimension
                }
            }
            
        except Exception as e:
            self.logger.error(f"获取集合统计信息失败: {e}")
            return {}
