#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ORC MongoDB服务队列控制功能使用示例

演示如何使用队列控制功能来管理Redis队列长度
"""

import asyncio
import aiohttp
import time
from typing import Dict, Any


class QueueControlExample:
    """队列控制使用示例"""
    
    def __init__(self, orc_service_url: str = "http://localhost:8001"):
        self.orc_service_url = orc_service_url
        
    async def check_service_health(self) -> Dict[str, Any]:
        """检查服务健康状态"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.orc_service_url}/health") as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        return {"error": f"HTTP {response.status}"}
        except Exception as e:
            return {"error": str(e)}
    
    async def get_queue_status(self) -> Dict[str, Any]:
        """获取队列状态"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.orc_service_url}/queue/status") as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        return {"error": f"HTTP {response.status}"}
        except Exception as e:
            return {"error": str(e)}
    
    async def get_service_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.orc_service_url}/stats") as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        return {"error": f"HTTP {response.status}"}
        except Exception as e:
            return {"error": str(e)}
    
    async def submit_processing_task(self, orc_file: str, process_date: str, prov_id: int) -> Dict[str, Any]:
        """提交ORC文件处理任务"""
        try:
            task_data = {
                "orc_file": orc_file,
                "process_date": process_date,
                "prov_id": prov_id
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(f"{self.orc_service_url}/process", json=task_data) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        return {"error": f"HTTP {response.status}"}
        except Exception as e:
            return {"error": str(e)}
    
    async def monitor_queue_with_control(self, duration: int = 300, interval: int = 10):
        """监控队列状态，演示队列控制功能"""
        print("=== 队列控制功能监控示例 ===")
        print(f"监控时长: {duration}秒，检查间隔: {interval}秒")
        print()
        
        start_time = time.time()
        last_status = None
        
        while time.time() - start_time < duration:
            # 获取队列状态
            queue_status = await self.get_queue_status()
            
            if "error" not in queue_status:
                current_time = time.strftime("%H:%M:%S")
                queue_length = queue_status.get("queue_length", 0)
                is_paused = queue_status.get("is_paused", False)
                pause_duration = queue_status.get("pause_duration")
                thresholds = queue_status.get("thresholds", {})
                
                # 检查状态变化
                current_status = "paused" if is_paused else "running"
                if current_status != last_status:
                    if current_status == "paused":
                        print(f"🔴 [{current_time}] 队列处理已暂停")
                        print(f"   队列长度: {queue_length}")
                        print(f"   暂停阈值: {thresholds.get('pause_threshold', 'N/A')}")
                    else:
                        print(f"🟢 [{current_time}] 队列处理已恢复")
                        print(f"   队列长度: {queue_length}")
                        print(f"   恢复阈值: {thresholds.get('resume_threshold', 'N/A')}")
                    print()
                    last_status = current_status
                
                # 显示当前状态
                status_icon = "🔴" if is_paused else "🟢"
                pause_info = f", 暂停时长: {pause_duration:.1f}s" if pause_duration else ""
                
                print(f"{status_icon} [{current_time}] 队列长度: {queue_length:,}, 状态: {current_status}{pause_info}")
                
                # 显示阈值信息
                if queue_length > 0:
                    pause_threshold = thresholds.get("pause_threshold", 0)
                    resume_threshold = thresholds.get("resume_threshold", 0)
                    
                    if pause_threshold > 0:
                        pause_percent = (queue_length / pause_threshold) * 100
                        print(f"   距离暂停阈值: {pause_percent:.1f}% ({queue_length}/{pause_threshold})")
                    
                    if resume_threshold > 0 and is_paused:
                        resume_percent = (queue_length / resume_threshold) * 100
                        print(f"   距离恢复阈值: {resume_percent:.1f}% ({queue_length}/{resume_threshold})")
            else:
                print(f"❌ 获取队列状态失败: {queue_status['error']}")
            
            await asyncio.sleep(interval)
        
        print("\n=== 监控结束 ===")
    
    async def demonstrate_queue_control(self):
        """演示队列控制功能的完整流程"""
        print("=== ORC MongoDB服务队列控制功能演示 ===\n")
        
        # 1. 检查服务健康状态
        print("1. 检查服务健康状态...")
        health = await self.check_service_health()
        if "error" in health:
            print(f"❌ 服务不可用: {health['error']}")
            return
        else:
            print(f"✅ 服务状态: {health.get('status', 'unknown')}")
            print(f"   运行时间: {health.get('uptime', 0):.1f}秒")
        print()
        
        # 2. 获取初始队列状态
        print("2. 获取初始队列状态...")
        initial_status = await self.get_queue_status()
        if "error" not in initial_status:
            print(f"   队列名称: {initial_status.get('queue_name', 'N/A')}")
            print(f"   当前长度: {initial_status.get('queue_length', 0):,}")
            print(f"   处理状态: {initial_status.get('status', 'unknown')}")
            
            thresholds = initial_status.get("thresholds", {})
            print(f"   暂停阈值: {thresholds.get('pause_threshold', 'N/A'):,}")
            print(f"   恢复阈值: {thresholds.get('resume_threshold', 'N/A'):,}")
        else:
            print(f"❌ 获取队列状态失败: {initial_status['error']}")
        print()
        
        # 3. 获取服务统计信息
        print("3. 获取服务统计信息...")
        stats = await self.get_service_stats()
        if "error" not in stats:
            service_stats = stats.get("stats", {})
            print(f"   总任务数: {service_stats.get('total_tasks', 0)}")
            print(f"   完成任务数: {service_stats.get('completed_tasks', 0)}")
            print(f"   失败任务数: {service_stats.get('failed_tasks', 0)}")
            print(f"   当前任务数: {stats.get('current_tasks', 0)}")
        else:
            print(f"❌ 获取统计信息失败: {stats['error']}")
        print()
        
        # 4. 开始监控
        print("4. 开始监控队列状态变化...")
        print("   (可以在另一个终端运行测试脚本来观察队列控制效果)")
        print("   测试命令: python3 services/orc_mongodb_service/test_queue_control.py --action add --count 10000")
        print()
        
        await self.monitor_queue_with_control(duration=180, interval=5)


async def main():
    """主函数"""
    example = QueueControlExample()
    
    try:
        await example.demonstrate_queue_control()
    except KeyboardInterrupt:
        print("\n用户中断，退出演示")
    except Exception as e:
        print(f"演示过程中发生错误: {e}")


if __name__ == "__main__":
    print("ORC MongoDB服务队列控制功能演示")
    print("请确保以下服务正在运行:")
    print("1. Redis服务 (localhost:6379)")
    print("2. ORC处理服务 (localhost:8001)")
    print("3. MongoDB写入服务 (localhost:8002)")
    print()
    
    asyncio.run(main())
