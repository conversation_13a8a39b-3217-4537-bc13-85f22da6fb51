#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Redis队列长度控制功能测试脚本

用于测试ORC处理服务的队列长度控制机制
"""

import asyncio
import json
import time
import redis.asyncio as redis
from typing import Dict, Any
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from shared.core import ConfigManager, Logger


class QueueControlTester:
    """队列控制测试器"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.logger = Logger(__name__, self.config_manager)
        self.redis_client = None
        
    async def initialize(self):
        """初始化测试器"""
        try:
            # 获取Redis配置
            config = self.config_manager.get_service_config("orc_processor_service")
            redis_config = config.get("redis", {})
            
            # 初始化Redis连接
            self.redis_client = redis.Redis(
                host=redis_config.get("host", "localhost"),
                port=redis_config.get("port", 6379),
                db=redis_config.get("db", 0),
                decode_responses=True
            )
            
            # 测试连接
            await self.redis_client.ping()
            self.logger.info("Redis连接初始化成功")
            
        except Exception as e:
            self.logger.error(f"初始化失败: {e}")
            raise
    
    async def get_queue_length(self, queue_name: str = "mongodb_write_queue") -> int:
        """获取队列长度"""
        try:
            return await self.redis_client.llen(queue_name)
        except Exception as e:
            self.logger.error(f"获取队列长度失败: {e}")
            return -1
    
    async def add_test_messages(self, count: int, queue_name: str = "mongodb_write_queue"):
        """向队列添加测试消息"""
        try:
            self.logger.info(f"开始向队列 {queue_name} 添加 {count} 条测试消息...")
            
            for i in range(count):
                test_message = {
                    "task_id": f"test_task_{i}_{int(time.time() * 1000)}",
                    "user_data": [
                        {
                            "uid": i + 1000000,
                            "pid_list": [f"pid_{j}" for j in range(10)],
                            "process_date": "20250630",
                            "prov_id": 200
                        }
                    ],
                    "process_date": "20250630",
                    "prov_id": 200,
                    "processed_at": time.time(),
                    "stats": {"test_message": True}
                }
                
                message_json = json.dumps(test_message, ensure_ascii=False)
                await self.redis_client.lpush(queue_name, message_json)
                
                if (i + 1) % 1000 == 0:
                    current_length = await self.get_queue_length(queue_name)
                    self.logger.info(f"已添加 {i + 1} 条消息，当前队列长度: {current_length}")
            
            final_length = await self.get_queue_length(queue_name)
            self.logger.info(f"完成添加测试消息，最终队列长度: {final_length}")
            
        except Exception as e:
            self.logger.error(f"添加测试消息失败: {e}")
    
    async def clear_queue(self, queue_name: str = "mongodb_write_queue"):
        """清空队列"""
        try:
            deleted_count = await self.redis_client.delete(queue_name)
            self.logger.info(f"已清空队列 {queue_name}，删除了 {deleted_count} 个键")
        except Exception as e:
            self.logger.error(f"清空队列失败: {e}")
    
    async def monitor_queue(self, duration: int = 300, interval: int = 5, 
                          queue_name: str = "mongodb_write_queue"):
        """监控队列长度变化"""
        try:
            self.logger.info(f"开始监控队列 {queue_name}，持续时间: {duration}秒，检查间隔: {interval}秒")
            
            start_time = time.time()
            while time.time() - start_time < duration:
                queue_length = await self.get_queue_length(queue_name)
                current_time = time.strftime("%H:%M:%S")
                
                self.logger.info(f"[{current_time}] 队列长度: {queue_length}")
                
                await asyncio.sleep(interval)
                
        except Exception as e:
            self.logger.error(f"监控队列失败: {e}")
    
    async def test_queue_control_thresholds(self):
        """测试队列控制阈值"""
        try:
            config = self.config_manager.get_service_config("orc_processor_service")
            redis_config = config.get("redis", {})
            queue_control = redis_config.get("queue_control", {})
            queue_name = redis_config.get("queue_name", "mongodb_write_queue")
            
            pause_threshold = queue_control.get("pause_threshold", 8000)
            resume_threshold = queue_control.get("resume_threshold", 5000)
            
            self.logger.info("=== 队列控制阈值测试 ===")
            self.logger.info(f"暂停阈值: {pause_threshold}")
            self.logger.info(f"恢复阈值: {resume_threshold}")
            
            # 1. 清空队列
            await self.clear_queue(queue_name)
            initial_length = await self.get_queue_length(queue_name)
            self.logger.info(f"初始队列长度: {initial_length}")
            
            # 2. 添加消息直到超过暂停阈值
            messages_to_add = pause_threshold + 1000
            self.logger.info(f"添加 {messages_to_add} 条消息以触发暂停阈值...")
            await self.add_test_messages(messages_to_add, queue_name)
            
            # 3. 检查队列长度
            current_length = await self.get_queue_length(queue_name)
            self.logger.info(f"当前队列长度: {current_length}")
            
            if current_length >= pause_threshold:
                self.logger.info("✅ 成功触发暂停阈值")
            else:
                self.logger.warning("❌ 未能触发暂停阈值")
            
            # 4. 模拟消费消息直到低于恢复阈值
            messages_to_consume = current_length - resume_threshold + 500
            self.logger.info(f"模拟消费 {messages_to_consume} 条消息...")
            
            for i in range(messages_to_consume):
                await self.redis_client.rpop(queue_name)
                if (i + 1) % 1000 == 0:
                    remaining_length = await self.get_queue_length(queue_name)
                    self.logger.info(f"已消费 {i + 1} 条消息，剩余队列长度: {remaining_length}")
            
            final_length = await self.get_queue_length(queue_name)
            self.logger.info(f"最终队列长度: {final_length}")
            
            if final_length <= resume_threshold:
                self.logger.info("✅ 成功触发恢复阈值")
            else:
                self.logger.warning("❌ 未能触发恢复阈值")
                
        except Exception as e:
            self.logger.error(f"测试队列控制阈值失败: {e}")
    
    async def cleanup(self):
        """清理资源"""
        if self.redis_client:
            await self.redis_client.close()


async def main():
    """主函数"""
    tester = QueueControlTester()
    
    try:
        await tester.initialize()
        
        # 解析命令行参数
        import argparse
        parser = argparse.ArgumentParser(description="Redis队列控制测试")
        parser.add_argument("--action", choices=["test", "monitor", "clear", "add"], 
                          default="test", help="执行的操作")
        parser.add_argument("--count", type=int, default=10000, help="添加消息数量")
        parser.add_argument("--duration", type=int, default=300, help="监控持续时间（秒）")
        parser.add_argument("--interval", type=int, default=5, help="监控检查间隔（秒）")
        parser.add_argument("--queue", default="mongodb_write_queue", help="队列名称")
        
        args = parser.parse_args()
        
        if args.action == "test":
            await tester.test_queue_control_thresholds()
        elif args.action == "monitor":
            await tester.monitor_queue(args.duration, args.interval, args.queue)
        elif args.action == "clear":
            await tester.clear_queue(args.queue)
        elif args.action == "add":
            await tester.add_test_messages(args.count, args.queue)
            
    except Exception as e:
        print(f"测试失败: {e}")
    finally:
        await tester.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
