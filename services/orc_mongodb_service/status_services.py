#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ORC MongoDB服务状态检查脚本

检查所有相关微服务的运行状态
"""

import subprocess
import requests
import sys
from pathlib import Path

# 颜色定义
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    NC = '\033[0m'  # No Color

class Logger:
    """简单的日志记录器"""
    
    @staticmethod
    def info(message: str):
        print(f"{Colors.GREEN}[INFO]{Colors.NC} {message}")
    
    @staticmethod
    def warn(message: str):
        print(f"{Colors.YELLOW}[WARN]{Colors.NC} {message}")
    
    @staticmethod
    def error(message: str):
        print(f"{Colors.RED}[ERROR]{Colors.NC} {message}")
    
    @staticmethod
    def success(message: str):
        print(f"{Colors.CYAN}[SUCCESS]{Colors.NC} {message}")

def check_tmux_session(session_name: str) -> bool:
    """检查tmux会话是否存在"""
    try:
        result = subprocess.run(
            ["tmux", "has-session", "-t", session_name],
            capture_output=True
        )
        return result.returncode == 0
    except Exception:
        return False

def check_service_health(host: str, port: int, service_name: str) -> bool:
    """检查服务健康状态"""
    try:
        response = requests.get(f"http://{host}:{port}/health", timeout=5)
        return response.status_code == 200
    except Exception:
        return False

def main():
    """主函数"""
    logger = Logger()
    
    logger.info("=== ORC MongoDB服务状态检查 ===")
    
    # 服务配置
    services = {
        "ORC处理服务": {
            "tmux_session": "orc-mongodb-services-orc",
            "host": "localhost",
            "port": 8001
        },
        "MongoDB写入服务": {
            "tmux_session": "orc-mongodb-services-mongodb", 
            "host": "localhost",
            "port": 8002
        }
    }
    
    all_healthy = True
    
    for service_name, config in services.items():
        print(f"\n{Colors.BLUE}检查 {service_name}...{Colors.NC}")
        
        # 检查tmux会话
        session_running = check_tmux_session(config["tmux_session"])
        if session_running:
            logger.success(f"Tmux会话 '{config['tmux_session']}' 正在运行")
        else:
            logger.error(f"Tmux会话 '{config['tmux_session']}' 未运行")
            all_healthy = False
            continue
        
        # 检查服务健康状态
        health_ok = check_service_health(config["host"], config["port"], service_name)
        if health_ok:
            logger.success(f"服务健康检查通过 (http://{config['host']}:{config['port']})")
        else:
            logger.error(f"服务健康检查失败 (http://{config['host']}:{config['port']})")
            all_healthy = False
    
    print(f"\n{Colors.BLUE}=== 总体状态 ==={Colors.NC}")
    if all_healthy:
        logger.success("所有服务运行正常")
        
        print(f"\n{Colors.BLUE}管理命令:{Colors.NC}")
        print("  查看所有tmux会话: tmux list-sessions")
        print("  连接到ORC处理服务: tmux attach-session -t orc-mongodb-services-orc")
        print("  连接到MongoDB写入服务: tmux attach-session -t orc-mongodb-services-mongodb")
        print("  停止所有服务: python3 services/orc_mongodb_service/start_services.py --stop")
        
        sys.exit(0)
    else:
        logger.error("部分服务存在问题")
        sys.exit(1)

if __name__ == "__main__":
    main()
