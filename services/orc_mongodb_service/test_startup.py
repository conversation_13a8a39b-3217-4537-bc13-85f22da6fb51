#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ORC MongoDB服务启动测试脚本

测试新的启动脚本功能，但不实际启动服务
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent.parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

from services.orc_mongodb_service.start_services import ServiceManager, Logger

def test_service_manager():
    """测试服务管理器功能"""
    logger = Logger()
    
    logger.info("=== 测试ORC MongoDB服务启动脚本 ===")
    
    # 创建服务管理器
    manager = ServiceManager()
    
    # 测试环境检查
    logger.info("1. 测试环境检查...")
    
    python_ok = manager.check_python_environment()
    tmux_ok = manager.check_tmux_available()
    config_ok = manager.check_config_file('configs/orc_mongodb_service/development.yaml')
    
    if python_ok and tmux_ok and config_ok:
        logger.success("所有环境检查通过")
    else:
        logger.error("环境检查失败")
        return False
    
    # 测试服务配置
    logger.info("2. 测试服务配置...")
    
    services = manager.services
    logger.info(f"配置的服务数量: {len(services)}")
    
    for service_key, service in services.items():
        logger.info(f"  - {service.name}: {service.module_path} (端口: {service.port})")
        logger.info(f"    Tmux会话: {service.tmux_session}")
    
    # 测试配置文件设置
    logger.info("3. 测试配置文件设置...")
    
    manager.set_config_file('configs/orc_mongodb_service/production.yaml')
    if manager.config_file == 'configs/orc_mongodb_service/production.yaml':
        logger.success("配置文件设置成功")
    else:
        logger.error("配置文件设置失败")
        return False
    
    # 测试端口设置
    logger.info("4. 测试端口设置...")
    
    manager.set_service_ports(orc_port=9001, mongodb_port=9002)
    
    if (manager.services["orc_processor"].port == 9001 and 
        manager.services["mongodb_writer"].port == 9002):
        logger.success("端口设置成功")
    else:
        logger.error("端口设置失败")
        return False
    
    # 测试清理现有会话（应该不会出错，即使没有会话）
    logger.info("5. 测试会话清理...")
    
    try:
        manager.kill_existing_sessions()
        logger.success("会话清理测试通过")
    except Exception as e:
        logger.error(f"会话清理测试失败: {e}")
        return False
    
    logger.success("=== 所有测试通过 ===")
    logger.info("启动脚本已准备就绪，可以正常使用")
    
    return True

def test_individual_service_imports():
    """测试各个服务模块是否可以正常导入"""
    logger = Logger()
    
    logger.info("=== 测试服务模块导入 ===")
    
    try:
        # 测试ORC处理服务
        logger.info("测试ORC处理服务模块导入...")
        from services.orc_mongodb_service.orc_processor_service.main import create_argument_parser as orc_parser
        orc_parser()
        logger.success("ORC处理服务模块导入成功")
        
        # 测试MongoDB写入服务
        logger.info("测试MongoDB写入服务模块导入...")
        from services.orc_mongodb_service.mongodb_writer_service.main import create_argument_parser as mongodb_parser
        mongodb_parser()
        logger.success("MongoDB写入服务模块导入成功")
        
        logger.success("所有服务模块导入测试通过")
        return True
        
    except Exception as e:
        logger.error(f"服务模块导入失败: {e}")
        return False

def main():
    """主函数"""
    logger = Logger()
    
    try:
        # 测试服务管理器
        if not test_service_manager():
            sys.exit(1)
        
        # 测试服务模块导入
        if not test_individual_service_imports():
            sys.exit(1)
        
        logger.success("=== 所有测试完成 ===")
        logger.info("新的启动脚本已准备就绪，可以使用以下命令启动服务：")
        logger.info("python3 services/orc_mongodb_service/start_services.py")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
