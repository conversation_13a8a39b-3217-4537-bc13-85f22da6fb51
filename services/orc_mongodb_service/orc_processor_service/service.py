#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ORC数据处理微服务

异步微服务，负责ORC文件处理和数据发送到消息队列
"""

import os
import sys
import asyncio
import json
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
import redis.asyncio as redis
from fastapi import FastAPI, HTTPException, BackgroundTasks
from pydantic import BaseModel
import uvicorn

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from shared.core import Config<PERSON><PERSON><PERSON>, <PERSON><PERSON>, ExceptionHandler
from shared.database.milvus import MilvusPool, MilvusVectorOperations
from shared.database.hive import HiveConnector
from shared.utils import DataProcessor, TimeUtils


@dataclass
class ProcessingTask:
    """处理任务数据结构"""
    task_id: str
    orc_file: str
    process_date: str
    prov_id: int
    created_at: float
    status: str = "pending"


@dataclass
class ProcessingResult:
    """处理结果数据结构"""
    task_id: str
    user_data: List[Dict[str, Any]]
    process_date: str
    prov_id: int
    processed_at: float
    stats: Dict[str, Any]


class ProcessTaskRequest(BaseModel):
    """处理任务请求模型"""
    orc_file: str
    process_date: str
    prov_id: int


class ORCProcessorService:
    """ORC数据处理微服务"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.config = config_manager.get_service_config("orc_processor_service")
        self.logger = Logger(__name__, config_manager)
        self.exception_handler = ExceptionHandler(self.logger)
        
        # 初始化组件
        self.hive_connector = HiveConnector(config_manager)
        self.data_processor = DataProcessor()
        self.milvus_pool = None
        self.redis_client = None
        
        # 服务状态
        self.is_running = False
        self.current_tasks = {}
        self.stats = {
            "total_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "total_users_processed": 0,
            "total_files_processed": 0,
            "start_time": time.time()
        }

        # 队列控制状态
        self.queue_paused = False
        self.queue_pause_start_time = None
        
        # 创建FastAPI应用
        self.app = FastAPI(title="ORC Processor Service", version="1.0.0")
        self._setup_routes()
    
    async def initialize(self):
        """初始化服务"""
        try:
            self.logger.info("初始化ORC数据处理微服务...")
            
            # 初始化Milvus连接池
            self.milvus_pool = MilvusPool(self.config_manager)
            await self.milvus_pool.initialize()
            
            # 初始化Redis连接
            redis_config = self.config.get("redis", {})
            self.redis_client = redis.Redis(
                host=redis_config.get("host", "localhost"),
                port=redis_config.get("port", 6379),
                db=redis_config.get("db", 0),
                decode_responses=True
            )
            
            # 测试Redis连接
            await self.redis_client.ping()
            
            self.is_running = True
            self.logger.info("ORC数据处理微服务初始化完成")
            
        except Exception as e:
            self.logger.error(f"服务初始化失败: {e}")
            raise
    
    async def shutdown(self):
        """关闭服务"""
        try:
            self.logger.info("关闭ORC数据处理微服务...")
            self.is_running = False
            
            if self.redis_client:
                await self.redis_client.close()
            
            if self.milvus_pool:
                await self.milvus_pool.close()
            
            self.logger.info("ORC数据处理微服务已关闭")
            
        except Exception as e:
            self.logger.error(f"服务关闭失败: {e}")
    
    def _setup_routes(self):
        """设置API路由"""
        
        @self.app.get("/health")
        async def health_check():
            """健康检查"""
            return {
                "status": "healthy" if self.is_running else "unhealthy",
                "service": "orc_processor_service",
                "version": "1.0.0",
                "uptime": time.time() - self.stats["start_time"],
                "stats": self.stats
            }
        
        @self.app.get("/stats")
        async def get_stats():
            """获取统计信息"""
            return {
                "stats": self.stats,
                "current_tasks": len(self.current_tasks),
                "task_details": list(self.current_tasks.keys())
            }

        @self.app.get("/queue/status")
        async def get_queue_status():
            """获取队列状态"""
            try:
                redis_config = self.config.get("redis", {})
                queue_name = redis_config.get("queue_name", "mongodb_write_queue")
                queue_length = await self.redis_client.llen(queue_name)

                queue_control = redis_config.get("queue_control", {})
                pause_threshold = queue_control.get("pause_threshold", 8000)
                resume_threshold = queue_control.get("resume_threshold", 5000)

                current_time = time.time()
                pause_duration = None
                if self.queue_paused and self.queue_pause_start_time:
                    pause_duration = current_time - self.queue_pause_start_time

                return {
                    "queue_name": queue_name,
                    "queue_length": queue_length,
                    "is_paused": self.queue_paused,
                    "pause_duration": pause_duration,
                    "thresholds": {
                        "pause_threshold": pause_threshold,
                        "resume_threshold": resume_threshold
                    },
                    "status": "paused" if self.queue_paused else "running"
                }
            except Exception as e:
                return {"error": str(e)}
        
        @self.app.post("/process")
        async def process_file(request: ProcessTaskRequest, background_tasks: BackgroundTasks):
            """处理ORC文件"""
            task_id = f"task_{int(time.time() * 1000)}"
            
            task = ProcessingTask(
                task_id=task_id,
                orc_file=request.orc_file,
                process_date=request.process_date,
                prov_id=request.prov_id,
                created_at=time.time()
            )
            
            self.current_tasks[task_id] = task
            background_tasks.add_task(self._process_file_async, task)
            
            return {
                "task_id": task_id,
                "status": "accepted",
                "message": "任务已接受，开始处理"
            }
        
        @self.app.get("/task/{task_id}")
        async def get_task_status(task_id: str):
            """获取任务状态"""
            if task_id not in self.current_tasks:
                raise HTTPException(status_code=404, detail="任务不存在")
            
            task = self.current_tasks[task_id]
            return asdict(task)
    
    async def _process_file_async(self, task: ProcessingTask):
        """异步处理文件"""
        try:
            self.logger.info(f"开始处理任务: {task.task_id}, 文件: {task.orc_file}")
            task.status = "processing"
            self.stats["total_tasks"] += 1
            
            # 处理ORC文件
            user_data_list = await self._process_orc_file(task.orc_file, task.process_date, task.prov_id)
            
            # 创建处理结果
            result = ProcessingResult(
                task_id=task.task_id,
                user_data=user_data_list,
                process_date=task.process_date,
                prov_id=task.prov_id,
                processed_at=time.time(),
                stats={
                    "total_users": len(user_data_list),
                    "processing_time": time.time() - task.created_at
                }
            )
            
            # 检查队列长度，必要时等待
            await self._wait_for_queue_space()

            # 发送到Redis队列
            await self._send_to_queue(result)
            
            task.status = "completed"
            self.stats["completed_tasks"] += 1
            self.stats["total_users_processed"] += len(user_data_list)
            self.stats["total_files_processed"] += 1
            
            self.logger.info(f"任务完成: {task.task_id}, 处理用户数: {len(user_data_list)}")
            
        except Exception as e:
            self.logger.error(f"任务处理失败: {task.task_id}, 错误: {e}")
            task.status = "failed"
            self.stats["failed_tasks"] += 1
        
        finally:
            # 清理完成的任务（保留一段时间用于查询）
            await asyncio.sleep(300)  # 5分钟后清理
            if task.task_id in self.current_tasks:
                del self.current_tasks[task.task_id]
    
    async def _process_orc_file(self, orc_file: str, process_date: str, prov_id: int) -> List[Dict[str, Any]]:
        """处理ORC文件"""
        try:
            # 使用HiveConnector读取ORC文件
            df = self.hive_connector.read_orc_file(orc_file)

            # 简单的数据处理示例（需要根据实际需求调整）
            user_data_list = []

            # 假设ORC文件包含uid和pid_list列
            for _, row in df.iterrows():
                user_data = {
                    "uid": row.get("uid", row.get("id", row.get("user_id"))),
                    "pid_list": row.get("pid_list", row.get("pic_id_list", [])),
                    "process_date": process_date,
                    "prov_id": prov_id,
                    "processed_at": TimeUtils.get_current_timestamp()
                }

                # 基本数据验证
                if user_data["uid"] and user_data["pid_list"]:
                    user_data_list.append(user_data)

            self.logger.info(f"ORC文件处理完成: {orc_file}, 处理用户数: {len(user_data_list)}")
            return user_data_list

        except Exception as e:
            self.logger.error(f"ORC文件处理失败: {orc_file}, 错误: {e}")
            raise

    async def _check_queue_length(self) -> bool:
        """
        检查Redis队列长度，决定是否需要暂停处理

        Returns:
            bool: True表示可以继续处理，False表示需要暂停
        """
        try:
            redis_config = self.config.get("redis", {})
            queue_name = redis_config.get("queue_name", "mongodb_write_queue")
            queue_control = redis_config.get("queue_control", {})

            # 获取队列长度
            queue_length = await self.redis_client.llen(queue_name)

            # 获取配置参数
            pause_threshold = queue_control.get("pause_threshold", 8000)
            resume_threshold = queue_control.get("resume_threshold", 5000)

            current_time = time.time()

            # 检查是否需要暂停
            if not self.queue_paused and queue_length >= pause_threshold:
                self.queue_paused = True
                self.queue_pause_start_time = current_time
                self.logger.warning(f"Redis队列长度达到暂停阈值 {pause_threshold}，当前长度: {queue_length}，暂停ORC处理")
                return False

            # 检查是否可以恢复
            elif self.queue_paused:
                # 只根据恢复阈值决定是否恢复
                if queue_length <= resume_threshold:
                    self.queue_paused = False
                    pause_duration = current_time - (self.queue_pause_start_time or current_time)
                    self.queue_pause_start_time = None
                    self.logger.info(f"Redis队列长度降至恢复阈值 {resume_threshold}，当前长度: {queue_length}，恢复ORC处理，暂停时长: {pause_duration:.1f}秒")
                    return True
                else:
                    # 仍需要等待，直到队列长度降到恢复阈值以下
                    wait_time = current_time - (self.queue_pause_start_time or current_time)
                    self.logger.debug(f"队列仍在暂停中，当前长度: {queue_length}，已等待: {wait_time:.1f}秒")
                    return False

            # 正常状态，可以继续处理
            return True

        except Exception as e:
            self.logger.error(f"检查队列长度失败: {e}")
            # 出错时允许继续处理，避免阻塞
            return True

    async def _wait_for_queue_space(self):
        """等待队列有空间可用"""
        redis_config = self.config.get("redis", {})
        queue_control = redis_config.get("queue_control", {})
        check_interval = queue_control.get("check_interval", 5)

        while self.is_running:
            if await self._check_queue_length():
                break

            # 等待一段时间后再检查
            await asyncio.sleep(check_interval)

    async def _send_to_queue(self, result: ProcessingResult):
        """发送处理结果到Redis队列"""
        try:
            queue_name = self.config.get("redis", {}).get("queue_name", "mongodb_write_queue")
            
            # 序列化结果
            result_json = json.dumps(asdict(result), ensure_ascii=False)
            
            # 发送到Redis队列
            await self.redis_client.lpush(queue_name, result_json)
            
            self.logger.debug(f"结果已发送到队列: {queue_name}, 任务ID: {result.task_id}")
            
        except Exception as e:
            self.logger.error(f"发送结果到队列失败: {e}")
            raise
    
    def run(self, host: str = "0.0.0.0", port: int = 8001):
        """运行服务"""
        uvicorn.run(
            self.app,
            host=host,
            port=port,
            log_level="info"
        )


async def create_service():
    """创建服务实例"""
    config_manager = ConfigManager()
    service = ORCProcessorService(config_manager)
    await service.initialize()
    return service


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="ORC数据处理微服务")
    parser.add_argument("--host", default="0.0.0.0", help="服务主机地址")
    parser.add_argument("--port", type=int, default=8001, help="服务端口")
    parser.add_argument("--config", help="配置文件路径")
    
    args = parser.parse_args()
    
    # 设置配置文件路径
    if args.config:
        os.environ['USER_DF_CONFIG_FILE'] = args.config
    
    # 创建并运行服务
    async def main():
        service = await create_service()
        try:
            service.run(host=args.host, port=args.port)
        finally:
            await service.shutdown()
    
    asyncio.run(main())
