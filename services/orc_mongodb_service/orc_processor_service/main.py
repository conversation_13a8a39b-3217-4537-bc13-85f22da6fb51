#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ORC数据处理微服务主入口

启动ORC数据处理微服务
"""

import os
import sys
import asyncio
import argparse
import signal
from typing import Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from shared.core import ConfigManager, Logger
from services.orc_mongodb_service.orc_processor_service.service import ORCProcessorService


class ServiceRunner:
    """服务运行器"""
    
    def __init__(self):
        self.service: Optional[ORCProcessorService] = None
        self.shutdown_requested = False
        self.logger = None
    
    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            self.logger.info(f"接收到信号 {signum}，准备关闭服务...")
            self.shutdown_requested = True
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def run(self, args):
        """运行服务"""
        try:
            # 初始化配置管理器
            config_manager = ConfigManager()
            self.logger = Logger(__name__, config_manager)
            
            # 设置信号处理器
            self.setup_signal_handlers()
            
            self.logger.info("=== ORC数据处理微服务启动 ===")
            
            # 创建服务实例
            self.service = ORCProcessorService(config_manager)
            await self.service.initialize()
            
            # 运行服务
            self.logger.info(f"服务启动在 {args.host}:{args.port}")
            
            # 创建服务器任务
            import uvicorn
            config = uvicorn.Config(
                self.service.app,
                host=args.host,
                port=args.port,
                log_level="info"
            )
            server = uvicorn.Server(config)
            
            # 启动服务器
            await server.serve()
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"服务运行失败: {e}")
            else:
                print(f"服务运行失败: {e}", file=sys.stderr)
            sys.exit(1)
        
        finally:
            if self.service:
                await self.service.shutdown()


def create_argument_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="ORC数据处理微服务",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 启动服务（使用默认配置）
  python3 services/orc_processor_service/main.py

  # 指定主机和端口
  python3 services/orc_processor_service/main.py --host 0.0.0.0 --port 8001

  # 指定配置文件
  python3 services/orc_processor_service/main.py --config configs/orc_processor_service/production.yaml
        """
    )
    
    # 服务配置参数
    parser.add_argument("--host", default="0.0.0.0", help="服务主机地址")
    parser.add_argument("--port", type=int, default=8001, help="服务端口")
    
    # 配置文件参数
    parser.add_argument("--config", help="配置文件路径")
    parser.add_argument("--config-dir", help="配置目录路径")
    parser.add_argument("--environment", choices=["development", "production"], 
                       default="development", help="运行环境")
    
    # 日志参数
    parser.add_argument("--log-level", choices=["DEBUG", "INFO", "WARNING", "ERROR"], 
                       help="日志级别")
    
    return parser


def validate_arguments(args):
    """验证命令行参数"""
    if args.port < 1 or args.port > 65535:
        raise ValueError("端口号必须在1-65535之间")


async def main():
    """主函数"""
    try:
        # 解析命令行参数
        parser = create_argument_parser()
        args = parser.parse_args()
        
        # 验证参数
        validate_arguments(args)
        
        # 设置环境变量
        if args.config:
            os.environ['USER_DF_CONFIG_FILE'] = args.config
        if args.config_dir:
            os.environ['USER_DF_CONFIG_DIR'] = args.config_dir
        if args.environment:
            os.environ['USER_DF_ENV'] = args.environment
        if args.log_level:
            os.environ['USER_DF_LOG_LEVEL'] = args.log_level
        
        # 创建并运行服务
        runner = ServiceRunner()
        await runner.run(args)
        
    except Exception as e:
        print(f"启动失败: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
