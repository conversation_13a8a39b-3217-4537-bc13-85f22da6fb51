# ORC MongoDB服务 - 生产环境配置文件
# 版本: 2.0.0
# 包含所有服务相关配置：MongoDB、Milvus、ORC处理等

# ==================== 全局配置 ====================
project:
  name: "User-DF"
  version: "2.0.0"
  environment: "production"

cache:
  enabled: true
  ttl: 3600

monitoring:
  enabled: true
  metrics_interval: 60

# ==================== 日期和省份配置 ====================
# 处理开始日期 (YYYYMMDD格式) - 生产环境处理更大的日期范围
start_date: "20250626"

# 处理结束日期 (YYYYMMDD格式)
end_date: "20250706"

# 省份ID列表 - 生产环境处理所有省份
province_ids: [100, 200, 210, 250, 531, 571]

# ==================== ORC文件配置 ====================
# ORC文件基础路径 - 生产环境使用正式数据路径
orc_base_path: "/workdir/hive_data/tw_user_pic_daily_aggregation"

# ORC文件匹配模式
orc_file_pattern: "*"

# 支持的ORC文件列名映射
column_mapping:
  uid_columns: ["id", "uid", "user_id", "userid", "UID", "USER_ID"]
  pid_columns: ["pic_id_list", "pid_list", "pid", "product_id", "item_id", "PID", "PRODUCT_ID"]

# ==================== 数据处理配置 ====================
# 每个用户最多保留的PID数量 - 生产环境使用完整配置
max_pids_per_user: 200

# 是否启用PID去重
enable_pid_deduplication: true

# 是否按时间戳排序PID
sort_pids_by_timestamp: true

# PID存储优化：是否按时间戳分组
group_pids_by_timestamp: true

# ==================== 分批处理配置（简化配置）====================
# 分批处理配置 - 生产环境优化，简化后的核心配置
batch_processing:
  # 用户处理批次大小（核心参数）
  batch_size: 1000
  # PID查询批次大小（发送给Milvus）- 生产环境增大
  pid_query_batch_size: 30000
  # 是否启用批量PID查询优化
  enable_batch_optimization: true

# ==================== MongoDB配置 ====================
mongodb:
  # 连接配置
  connection:
    # 是否使用完整URI连接（推荐用于生产环境）
    use_uri: false
    
    # 完整连接URI（当use_uri为true时使用）
    uri: "**************************************************************************************"
    
    # 分离参数连接（当use_uri为false时使用）
    host: "localhost"
    port: 27017
    username: ""
    password: ""
    
    # 认证数据库
    auth_source: "admin"
  
  # 数据库名称
  database: "nrdc"
  
  # 连接池配置
  # 针对20亿用户量级优化
  connection_pool:
    # 最大连接池大小 - 生产环境增大
    max_pool_size: 200
    # 最小连接池大小
    min_pool_size: 20
    # 连接最大空闲时间（毫秒）
    max_idle_time_ms: 30000
    # 连接最大生存时间（毫秒）
    max_life_time_ms: 600000

  # 写入性能优化配置
  write_optimization:
    # 写确认级别配置
    write_concern:
      # 写确认级别: 1=主节点确认, "majority"=大多数节点确认
      # 移除w: 0（无确认）选项以避免MongoDB内存占用过大而崩溃
      w: 1
      # 是否等待日志写入磁盘 (false可提升性能，但降低数据持久性保证)
      j: false
      # 写入超时时间（毫秒）
      wtimeout: 30000

    # 批量操作配置
    bulk_operations:
      # 是否启用乱序插入（true可提升性能，但可能改变错误处理行为）
      unordered_inserts: true
      # 是否在批量操作中跳过文档验证（true可提升性能）
      bypass_document_validation: true
  
  # 集合配置
  collections:
    # 用户PID记录集合
    user_pid_collection: "user_pid_records_optimized"
    # 用户向量集合
    user_vector_collection: "user_vectors"
  
  # 用户数据配置
  user_data:
    # 每个用户最多保留的PID数量（针对20亿用户优化）
    max_pids_per_user: 200
    # 是否启用PID去重
    enable_pid_deduplication: true

# ==================== Milvus配置 ====================
milvus:
  # 连接配置
  connection:
    # 新的 MilvusClient 连接参数
    uri: "http://10.246.85.14:19530"  # 生产环境
    token: "nrdc_ilm:Nr@#dc12Ilm"
    database: "nrdc_db"
    
    # 连接池配置
    pool:
      # 最大连接数 - 生产环境增大
      max_connections: 100
      # 最小连接数
      min_connections: 10
      # 连接超时（秒）
      timeout: 30
      # 连接重试次数
      max_retries: 5
      # 重试延迟（秒）
      retry_delay: 2.0
  
  # 集合配置
  collections:
    # 内容塔集合名称（用于获取内容向量）
    content_collection: "content_tower_collection_20250616"
    # 用户塔集合名称（用于存储用户向量）
    user_collection: "user_tower_collection"
  
  # 向量维度配置
  vector_dimensions:
    # 内容向量维度
    content_vector_dim: 512
    # 用户向量维度
    user_vector_dim: 256
  
  # 索引配置
  index:
    # 默认索引类型
    default_type: "IVF_FLAT"
    # 索引参数
    parameters:
      IVF_FLAT:
        nlist: 2048  # 生产环境增大
      IVF_SQ8:
        nlist: 2048
      IVF_PQ:
        nlist: 2048
        m: 16
        nbits: 8
      HNSW:
        M: 32  # 生产环境增大
        efConstruction: 400
    # 构建索引的阈值
    build_threshold: 50000  # 生产环境增大
  
  # 搜索配置
  search:
    # 默认搜索参数
    default_params:
      # 搜索时的候选数量
      nprobe: 20  # 生产环境增大
      # HNSW搜索参数
      ef: 128
    # 搜索限制
    limits:
      # 最大返回结果数
      max_results: 1000
      # 默认返回结果数
      default_results: 10
    # 相似度阈值
    similarity:
      # 最小相似度分数
      min_score: 0.0
      # 默认相似度分数
      default_min_score: 0.5
  
  # 批处理配置
  batch_processing:
    # 批处理超时（秒）
    timeout: 600  # 生产环境增大
    # 并发批处理数量
    concurrent_batches: 10  # 生产环境增大

# ==================== Milvus服务配置 ====================

# 是否启用Milvus PID过滤
enable_milvus_filtering: true

# ==================== 处理配置 ====================
# 单进程处理模式（移除多进程支持）
single_process_mode: true

# ==================== 监控和日志配置 ====================
# 进度报告间隔（秒） - 生产环境增大间隔
progress_report_interval: 60

# 统计输出间隔（秒）
stats_output_interval: 300

# 是否启用详细统计
enable_detailed_stats: true

# 是否启用详细日志 - 生产环境关闭详细日志
enable_verbose_logging: false

# ==================== 日志配置 ====================
logging:
  # 统一日志配置 - 使用单一日志文件
  level: INFO
  format: "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
  file_enabled: true
  file_path: logs/orc_mongodb_service/orc_mongodb_service.log  # 统一日志文件，会自动添加时间戳后缀
  file_max_size: "100MB"  # 生产环境增大文件大小
  file_backup_count: 15   # 生产环境增加备份数量
  console_enabled: true
  console_colored: true   # 启用彩色输出以便于阅读
  structured: false       # 使用标准格式而非JSON格式，便于阅读