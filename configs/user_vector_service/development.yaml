# 用户向量服务 - 开发环境配置
# 版本: 2.0.0
# 包含所有服务相关配置：全局配置、MongoDB、Milvus、向量处理等

# ==================== 全局配置 ====================
project:
  name: "User-DF"
  version: "2.0.0"
  environment: "development"

cache:
  enabled: true
  ttl: 3600

monitoring:
  enabled: true
  metrics_interval: 60

# ==================== 数据过滤配置 ====================
# 用户数量限制（可选）- 开发环境限制处理用户数
user_limit: 1000

# MongoDB 查询过滤条件
mongodb_filters:
  # 日期范围过滤（基于 updated_days 字段）
  date_range:
    # 是否启用日期范围过滤
    enabled: false
    # 开始日期（YYYYMMDD格式，null表示不限制）
    start_date: null
    # 结束日期（YYYYMMDD格式，null表示不限制）
    end_date: null
    # 或者使用相对天数（最近N天）
    recent_days: null  # 如果设置，将覆盖 start_date 和 end_date

  # 向量状态过滤
  vector_status:
    # 是否只处理未存储向量的用户（is_stored=false）
    only_not_stored: true
    # 是否包括向量过期的用户（存储时间早于更新时间）
    include_expired: true

  # 省份过滤
  province:
    # 是否启用省份过滤
    enabled: false
    # 指定要处理的省份ID列表（如果为空，则处理所有省份）
    prov_ids: []  # 例如: [100, 200, 210]

# 查询排序配置
query_sorting:
  # 是否按更新时间排序
  sort_by_update_time: true
  # 排序方向（desc: 降序，asc: 升序）
  sort_direction: "desc"

# 最大PID数量限制
max_pids_per_user: 100



# ==================== 批处理配置 ====================
# 用户批次大小 - 开发环境使用较小批次
user_batch_size: 50

# 向量批次大小（用于Milvus操作）
vector_batch_size: 500

# ID范围批次处理配置
id_range_processing:
  # 是否启用ID范围批次处理
  enabled: true
  # ID范围跨度（每次从MongoDB获取的用户数量）- 开发环境使用较小批次
  span: 1000  # 每次获取1,000个用户
  # 可选的起始ID（null表示从最小ID开始）
  start_id: null
  # 可选的结束ID（null表示到最大ID结束）
  end_id: null

# ==================== 向量计算配置 ====================
# 最少PID要求（用户必须有至少这么多PID才能计算向量）
min_pids_required: 3

# 计算向量时使用的最大PID数量
max_pids_for_computation: 50

# 目标向量维度（降维后）
target_dimension: 256

# 源向量维度（内容向量维度）
source_dimension: 512

# 向量聚合方法
aggregation_method: "weighted_mean"  # weighted_mean, simple_mean, max_pooling

# 权重策略
weight_strategy: "recency"  # uniform, recency, frequency

# 时间衰减因子
time_decay_factor: 0.1

# 最大时间权重
max_time_weight: 2.0

# ==================== PCA配置 ====================
# PCA方法（使用预计算模型）
pca_method: "precomputed_pca"

# 预计算PCA模型路径
precomputed_pca_model_path: "models/pca_precomputed/latest_pca_model.pkl"

# PCA模型配置
pca_config:
  # 是否启用自动重载模型
  auto_reload: false

  # 模型检查间隔（秒）
  check_interval: 300

  # 随机状态种子（用于预计算训练）
  random_state: 42



# ==================== 监控配置 ====================
# 进度报告间隔（秒）
progress_report_interval: 10

# 统计输出间隔（秒）
stats_output_interval: 30

# 是否启用详细日志
enable_verbose_logging: true

# ==================== 运行模式配置 ====================
# 默认运行模式 - 简化为只有两种模式
default_processing_mode: "process_users"  # health_check, process_users

# 健康检查模式配置
health_check:
  # 是否启用详细健康检查
  detailed: true

# 用户处理模式配置
process_users:
  # 是否启用用户处理模式
  enabled: true

# 运行模式
run_mode: "batch"  # batch, continuous, test

# 测试模式配置
test_mode:
  enable_dry_run: false
  sample_size: 100
  skip_storage: false

# ==================== 日志配置 ====================
# 日志配置
logging:
  # 默认日志级别
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL

  # 是否启用详细输出
  verbose: false

  # 日志格式配置
  format:
    # 是否使用彩色输出
    colored: true
    # 是否包含时间戳
    include_timestamp: true
    # 是否包含文件名和行号
    include_location: false

# ==================== 环境配置 ====================
# 环境配置
environment:
  # 默认环境
  name: "development"  # development, testing, production

  # 配置文件目录
  config_dir: "configs"

  # 是否自动检测环境
  auto_detect: false

# ==================== MongoDB配置 ====================
mongodb:
  # 连接配置
  connection:
    # 是否使用完整URI连接（推荐用于生产环境）
    use_uri: false

    # 完整连接URI（当use_uri为true时使用）
    uri: "*****************************************************************"

    # 分离参数连接（当use_uri为false时使用）
    host: "localhost"
    port: 27017
    username: null  # 如果需要认证，请设置用户名
    password: null  # 如果需要认证，请设置密码

    # 认证数据库
    auth_source: "admin"

  # 数据库名称
  database: "nrdc"

  # 连接池配置
  connection_pool:
    # 最大连接池大小
    max_pool_size: 50
    # 最小连接池大小
    min_pool_size: 5
    # 连接最大空闲时间（毫秒）
    max_idle_time_ms: 30000
    # 连接最大生存时间（毫秒）
    max_life_time_ms: 600000

  # 写入性能优化配置
  write_optimization:
    # 写确认级别配置
    write_concern:
      # 写确认级别: 0=无确认, 1=主节点确认, "majority"=大多数节点确认
      w: 1
      # 是否等待日志写入磁盘 (false可提升性能)
      j: false
      # 写入超时时间（毫秒）
      wtimeout: 5000

    # 批量操作配置
    bulk_operations:
      # 是否启用乱序插入（true可提升性能，但可能改变错误处理行为）
      unordered_inserts: true
      # 是否在批量操作中跳过文档验证（true可提升性能）
      bypass_document_validation: false

  # 集合配置
  collections:
    # 用户PID记录集合
    user_pid_collection: "user_pid_records_optimized"
    # 用户向量集合
    user_vector_collection: "user_vectors"

# ==================== Milvus配置 ====================
milvus:
  # 连接配置
  connection:
    # 新的 MilvusClient 连接参数
    uri: "http://localhost:19530"   # 本地测试环境
    token: "passwd"
    database: "nrdc_db"

    # 连接池配置
    pool:
      # 最大连接数
      max_connections: 50
      # 最小连接数
      min_connections: 5
      # 连接超时（秒）
      timeout: 30
      # 连接重试次数
      max_retries: 5
      # 重试延迟（秒）
      retry_delay: 2.0

  # 集合配置
  collections:
    # 内容塔集合名称（用于获取内容向量）
    content_collection: "content_tower_collection_20250616"
    # 用户塔集合名称（用于存储用户向量）
    user_collection: "user_tower_collection"

  # 向量维度配置
  vector_dimensions:
    # 内容向量维度
    content_vector_dim: 512
    # 用户向量维度
    user_vector_dim: 256

  # 索引配置
  index:
    # 默认索引类型
    default_type: "IVF_FLAT"
    # 索引参数
    parameters:
      IVF_FLAT:
        nlist: 1024
      IVF_SQ8:
        nlist: 1024
      IVF_PQ:
        nlist: 1024
        m: 16
        nbits: 8
      HNSW:
        M: 16
        efConstruction: 200
    # 构建索引的阈值
    build_threshold: 10000

  # 搜索配置
  search:
    # 默认搜索参数
    default_params:
      # 搜索时的候选数量
      nprobe: 10
      # HNSW搜索参数
      ef: 64
    # 搜索限制
    limits:
      # 最大返回结果数
      max_results: 1000
      # 默认返回结果数
      default_results: 10
    # 相似度阈值
    similarity:
      # 最小相似度分数
      min_score: 0.0
      # 默认相似度分数
      default_min_score: 0.5

  # 批处理配置
  batch_processing:
    # 批量插入大小
    insert_batch_size: 1000
    # 批量搜索大小
    search_batch_size: 100
    # 批量删除大小
    delete_batch_size: 1000
    # 批处理超时（秒）
    timeout: 300
    # 并发批处理数量
    concurrent_batches: 5

# ==================== 日志配置 ====================
logging:
  # 统一日志配置 - 使用单一日志文件
  level: INFO
  format: "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
  file_enabled: true
  file_path: logs/user_vector_service/user_vector_service.log  # 统一日志文件，会自动添加时间戳后缀
  file_max_size: "50MB"  # 开发环境文件大小
  file_backup_count: 10   # 开发环境备份数量
  console_enabled: true
  console_colored: true   # 启用彩色输出以便于阅读
  structured: false       # 使用标准格式而非JSON格式，便于阅读