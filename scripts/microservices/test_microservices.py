#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微服务集成测试脚本

测试微服务架构的正确性，包括：
- 服务启动和健康检查
- 服务间通信
- 错误处理
- 性能测试
"""

import os
import sys
import asyncio
import json
import time
import requests
import redis
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from shared.core import ConfigManager, Logger


@dataclass
class TestResult:
    """测试结果"""
    test_name: str
    success: bool
    message: str
    duration: float
    details: Optional[Dict[str, Any]] = None


class MicroservicesTestSuite:
    """微服务测试套件"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.logger = Logger(__name__, self.config_manager)
        
        # 服务配置
        self.services = {
            "orc_processor": {
                "name": "ORC处理服务",
                "url": "http://localhost:8001",
                "health_endpoint": "/health",
                "stats_endpoint": "/stats"
            },
            "mongodb_writer": {
                "name": "MongoDB写入服务",
                "url": "http://localhost:8002", 
                "health_endpoint": "/health",
                "stats_endpoint": "/stats"
            }
        }
        
        # Redis配置
        self.redis_client = redis.Redis(
            host="localhost",
            port=6379,
            db=0,
            decode_responses=True
        )
        
        # 测试结果
        self.test_results: List[TestResult] = []
    
    def run_test(self, test_name: str, test_func, *args, **kwargs) -> TestResult:
        """运行单个测试"""
        self.logger.info(f"运行测试: {test_name}")
        start_time = time.time()
        
        try:
            result = test_func(*args, **kwargs)
            duration = time.time() - start_time
            
            if isinstance(result, tuple):
                success, message, details = result
            else:
                success = result
                message = "测试通过" if success else "测试失败"
                details = None
            
            test_result = TestResult(
                test_name=test_name,
                success=success,
                message=message,
                duration=duration,
                details=details
            )
            
        except Exception as e:
            duration = time.time() - start_time
            test_result = TestResult(
                test_name=test_name,
                success=False,
                message=f"测试异常: {str(e)}",
                duration=duration
            )
        
        self.test_results.append(test_result)
        
        status = "✅ 通过" if test_result.success else "❌ 失败"
        self.logger.info(f"{status} {test_name} ({duration:.2f}s): {test_result.message}")
        
        return test_result
    
    def test_service_health(self, service_key: str) -> tuple:
        """测试服务健康状态"""
        service_config = self.services[service_key]
        url = service_config["url"] + service_config["health_endpoint"]
        
        try:
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                status = data.get("status", "unknown")
                
                if status == "healthy":
                    return True, f"{service_config['name']} 健康状态正常", data
                else:
                    return False, f"{service_config['name']} 状态异常: {status}", data
            else:
                return False, f"{service_config['name']} HTTP错误: {response.status_code}", None
                
        except requests.exceptions.RequestException as e:
            return False, f"{service_config['name']} 连接失败: {str(e)}", None
    
    def test_service_stats(self, service_key: str) -> tuple:
        """测试服务统计接口"""
        service_config = self.services[service_key]
        url = service_config["url"] + service_config["stats_endpoint"]
        
        try:
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                return True, f"{service_config['name']} 统计接口正常", data
            else:
                return False, f"{service_config['name']} 统计接口HTTP错误: {response.status_code}", None
                
        except requests.exceptions.RequestException as e:
            return False, f"{service_config['name']} 统计接口连接失败: {str(e)}", None
    
    def test_redis_connection(self) -> tuple:
        """测试Redis连接"""
        try:
            # 测试ping
            pong = self.redis_client.ping()
            if not pong:
                return False, "Redis ping失败", None
            
            # 测试基本操作
            test_key = "microservices_test_key"
            test_value = "test_value"
            
            self.redis_client.set(test_key, test_value, ex=10)
            retrieved_value = self.redis_client.get(test_key)
            
            if retrieved_value != test_value:
                return False, "Redis读写测试失败", None
            
            # 清理测试数据
            self.redis_client.delete(test_key)
            
            return True, "Redis连接正常", {"ping": True, "read_write": True}
            
        except Exception as e:
            return False, f"Redis连接失败: {str(e)}", None
    
    def test_queue_operations(self) -> tuple:
        """测试队列操作"""
        try:
            queue_name = "test_queue"
            test_data = {"test": "data", "timestamp": time.time()}
            
            # 清理可能存在的测试队列
            self.redis_client.delete(queue_name)
            
            # 测试入队
            self.redis_client.lpush(queue_name, json.dumps(test_data))
            
            # 测试队列长度
            queue_length = self.redis_client.llen(queue_name)
            if queue_length != 1:
                return False, f"队列长度错误: 期望1，实际{queue_length}", None
            
            # 测试出队
            popped_data = self.redis_client.rpop(queue_name)
            if not popped_data:
                return False, "队列出队失败", None
            
            # 验证数据
            parsed_data = json.loads(popped_data)
            if parsed_data["test"] != test_data["test"]:
                return False, "队列数据验证失败", None
            
            # 清理
            self.redis_client.delete(queue_name)
            
            return True, "队列操作正常", {"enqueue": True, "dequeue": True, "data_integrity": True}
            
        except Exception as e:
            return False, f"队列操作失败: {str(e)}", None
    
    def test_orc_processor_api(self) -> tuple:
        """测试ORC处理服务API"""
        try:
            url = self.services["orc_processor"]["url"] + "/process"
            
            # 构造测试请求
            test_request = {
                "orc_file": "/test/path/test.orc",
                "process_date": "20250722",
                "prov_id": 200
            }
            
            response = requests.post(url, json=test_request, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                task_id = data.get("task_id")
                
                if task_id:
                    # 检查任务状态
                    time.sleep(1)  # 等待任务处理
                    
                    task_url = self.services["orc_processor"]["url"] + f"/task/{task_id}"
                    task_response = requests.get(task_url, timeout=5)
                    
                    if task_response.status_code == 200:
                        task_data = task_response.json()
                        return True, "ORC处理API正常", {
                            "task_created": True,
                            "task_id": task_id,
                            "task_status": task_data.get("status")
                        }
                    else:
                        return False, f"任务状态查询失败: {task_response.status_code}", None
                else:
                    return False, "任务创建失败: 无task_id", data
            else:
                return False, f"ORC处理API HTTP错误: {response.status_code}", None
                
        except requests.exceptions.RequestException as e:
            return False, f"ORC处理API连接失败: {str(e)}", None
    
    def test_mongodb_writer_queue_status(self) -> tuple:
        """测试MongoDB写入服务队列状态"""
        try:
            url = self.services["mongodb_writer"]["url"] + "/queue/status"
            
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                queue_name = data.get("queue_name")
                queue_length = data.get("queue_length")
                
                if queue_name and queue_length is not None:
                    return True, "MongoDB写入服务队列状态正常", data
                else:
                    return False, "队列状态数据不完整", data
            else:
                return False, f"队列状态API HTTP错误: {response.status_code}", None
                
        except requests.exceptions.RequestException as e:
            return False, f"队列状态API连接失败: {str(e)}", None
    
    def test_end_to_end_communication(self) -> tuple:
        """测试端到端通信"""
        try:
            # 1. 清理测试队列
            queue_name = "mongodb_write_queue"
            initial_length = self.redis_client.llen(queue_name)
            
            # 2. 向ORC处理服务提交任务
            orc_url = self.services["orc_processor"]["url"] + "/process"
            test_request = {
                "orc_file": "/test/path/integration_test.orc",
                "process_date": "20250722",
                "prov_id": 200
            }
            
            response = requests.post(orc_url, json=test_request, timeout=10)
            
            if response.status_code != 200:
                return False, f"任务提交失败: {response.status_code}", None
            
            task_data = response.json()
            task_id = task_data.get("task_id")
            
            if not task_id:
                return False, "任务ID获取失败", task_data
            
            # 3. 等待任务处理
            time.sleep(3)
            
            # 4. 检查队列是否有新消息
            final_length = self.redis_client.llen(queue_name)
            
            # 5. 检查任务状态
            task_url = self.services["orc_processor"]["url"] + f"/task/{task_id}"
            task_response = requests.get(task_url, timeout=5)
            
            task_status = "unknown"
            if task_response.status_code == 200:
                task_info = task_response.json()
                task_status = task_info.get("status", "unknown")
            
            return True, "端到端通信测试完成", {
                "task_id": task_id,
                "task_status": task_status,
                "initial_queue_length": initial_length,
                "final_queue_length": final_length,
                "queue_changed": final_length != initial_length
            }
            
        except Exception as e:
            return False, f"端到端通信测试失败: {str(e)}", None
    
    def run_all_tests(self):
        """运行所有测试"""
        self.logger.info("=== 开始微服务集成测试 ===")
        
        # 基础连接测试
        self.run_test("Redis连接测试", self.test_redis_connection)
        self.run_test("队列操作测试", self.test_queue_operations)
        
        # 服务健康检查
        for service_key in self.services:
            self.run_test(f"{self.services[service_key]['name']}健康检查", 
                         self.test_service_health, service_key)
        
        # 服务统计接口测试
        for service_key in self.services:
            self.run_test(f"{self.services[service_key]['name']}统计接口", 
                         self.test_service_stats, service_key)
        
        # API功能测试
        self.run_test("ORC处理API测试", self.test_orc_processor_api)
        self.run_test("MongoDB写入队列状态测试", self.test_mongodb_writer_queue_status)
        
        # 集成测试
        self.run_test("端到端通信测试", self.test_end_to_end_communication)
    
    def print_test_summary(self):
        """打印测试摘要"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result.success)
        failed_tests = total_tests - passed_tests
        
        print("\n" + "="*60)
        print("测试摘要")
        print("="*60)
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests} ✅")
        print(f"失败: {failed_tests} ❌")
        print(f"成功率: {(passed_tests/total_tests*100):.1f}%")
        
        if failed_tests > 0:
            print("\n失败的测试:")
            for result in self.test_results:
                if not result.success:
                    print(f"  ❌ {result.test_name}: {result.message}")
        
        print("="*60)
        
        return failed_tests == 0


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="微服务集成测试")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    # 创建测试套件
    test_suite = MicroservicesTestSuite()
    
    try:
        # 运行所有测试
        test_suite.run_all_tests()
        
        # 打印摘要
        success = test_suite.print_test_summary()
        
        # 返回适当的退出码
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"测试运行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
