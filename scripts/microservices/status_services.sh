#!/bin/bash
# -*- coding: utf-8 -*-
"""
检查所有微服务状态的脚本
"""

set -e

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
LOG_DIR="$PROJECT_ROOT/logs"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查单个服务状态
check_service_status() {
    local service_name=$1
    local port=$2
    local pid_file="$LOG_DIR/${service_name}.pid"
    
    echo "----------------------------------------"
    echo "服务: $service_name"
    echo "端口: $port"
    
    # 检查PID文件
    if [[ -f "$pid_file" ]]; then
        local pid=$(cat "$pid_file")
        echo "PID文件: $pid"
        
        # 检查进程是否存在
        if kill -0 "$pid" 2>/dev/null; then
            echo -e "进程状态: ${GREEN}运行中${NC}"
            
            # 检查端口是否监听
            if netstat -tuln 2>/dev/null | grep -q ":$port "; then
                echo -e "端口状态: ${GREEN}监听中${NC}"
            else
                echo -e "端口状态: ${YELLOW}未监听${NC}"
            fi
            
            # 检查HTTP健康状态
            if command -v curl &> /dev/null; then
                if curl -s "http://localhost:$port/health" > /dev/null 2>&1; then
                    echo -e "健康检查: ${GREEN}通过${NC}"
                    
                    # 获取详细状态
                    local health_info=$(curl -s "http://localhost:$port/health" 2>/dev/null)
                    if [[ -n "$health_info" ]]; then
                        echo "服务信息:"
                        echo "$health_info" | python3 -m json.tool 2>/dev/null || echo "$health_info"
                    fi
                else
                    echo -e "健康检查: ${RED}失败${NC}"
                fi
            else
                echo -e "健康检查: ${YELLOW}跳过 (curl未安装)${NC}"
            fi
        else
            echo -e "进程状态: ${RED}未运行${NC}"
            echo -e "端口状态: ${RED}未监听${NC}"
            echo -e "健康检查: ${RED}失败${NC}"
        fi
    else
        echo -e "PID文件: ${RED}不存在${NC}"
        echo -e "进程状态: ${RED}未运行${NC}"
        echo -e "端口状态: ${RED}未监听${NC}"
        echo -e "健康检查: ${RED}失败${NC}"
    fi
}

# 检查Redis状态
check_redis_status() {
    echo "----------------------------------------"
    echo "服务: Redis"
    
    if command -v redis-cli &> /dev/null; then
        if redis-cli ping &> /dev/null; then
            echo -e "连接状态: ${GREEN}正常${NC}"
            
            # 获取Redis信息
            local redis_info=$(redis-cli info server 2>/dev/null | head -5)
            if [[ -n "$redis_info" ]]; then
                echo "Redis信息:"
                echo "$redis_info"
            fi
            
            # 检查队列长度
            local queue_length=$(redis-cli llen mongodb_write_queue 2>/dev/null || echo "0")
            echo "队列长度: $queue_length"
        else
            echo -e "连接状态: ${RED}失败${NC}"
        fi
    else
        echo -e "连接状态: ${YELLOW}跳过 (redis-cli未安装)${NC}"
    fi
}

# 显示系统资源使用情况
show_system_resources() {
    echo "----------------------------------------"
    echo "系统资源使用情况"
    
    # CPU使用率
    if command -v top &> /dev/null; then
        local cpu_usage=$(top -l 1 -n 0 | grep "CPU usage" | awk '{print $3}' | sed 's/%//')
        echo "CPU使用率: ${cpu_usage}%"
    fi
    
    # 内存使用情况
    if command -v free &> /dev/null; then
        free -h | head -2
    elif command -v vm_stat &> /dev/null; then
        # macOS
        local mem_info=$(vm_stat | head -5)
        echo "内存信息:"
        echo "$mem_info"
    fi
    
    # 磁盘使用情况
    if command -v df &> /dev/null; then
        echo "磁盘使用情况:"
        df -h | head -2
        df -h "$PROJECT_ROOT" | tail -1
    fi
}

# 显示日志文件信息
show_log_info() {
    echo "----------------------------------------"
    echo "日志文件信息"
    
    if [[ -d "$LOG_DIR" ]]; then
        echo "日志目录: $LOG_DIR"
        
        # 显示最新的日志文件
        for service in orc_processor_service mongodb_writer_service monitoring_service; do
            local log_file="$LOG_DIR/${service}/${service}.log"
            if [[ -f "$log_file" ]]; then
                local file_size=$(du -h "$log_file" | cut -f1)
                local last_modified=$(stat -c %y "$log_file" 2>/dev/null || stat -f %Sm "$log_file" 2>/dev/null)
                echo "$service: $file_size, 最后修改: $last_modified"
            else
                echo "$service: 日志文件不存在"
            fi
        done
    else
        echo "日志目录不存在: $LOG_DIR"
    fi
}

# 主函数
main() {
    echo "========================================"
    echo "User-DF 微服务状态检查"
    echo "时间: $(date)"
    echo "========================================"
    
    # 检查各个服务状态
    check_service_status "orc_processor_service" 8001
    check_service_status "mongodb_writer_service" 8002
    
    # 检查Redis状态
    check_redis_status
    
    # 显示系统资源
    show_system_resources
    
    # 显示日志信息
    show_log_info
    
    echo "========================================"
    echo "状态检查完成"
    echo "========================================"
}

# 执行主函数
main "$@"
