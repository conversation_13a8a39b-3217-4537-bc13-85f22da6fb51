# 微服务启动方式迁移通知

## 重要变更

原有的shell脚本启动方式已被替换为Python启动脚本，并使用tmux替代nohup进行服务管理。

## 旧的启动方式（已移除）

```bash
# 这些脚本已被移除
./scripts/microservices/start_all_services.sh
./scripts/microservices/status_services.sh  
./scripts/microservices/stop_all_services.sh
```

## 新的启动方式

### ORC MongoDB服务

```bash
# 启动服务
python3 services/orc_mongodb_service/start_services.py

# 使用指定配置
python3 services/orc_mongodb_service/start_services.py --config configs/orc_mongodb_service/production.yaml

# 查看状态
python3 services/orc_mongodb_service/status_services.py

# 停止服务
python3 services/orc_mongodb_service/start_services.py --stop
```

## 主要改进

1. **Python脚本替代Shell脚本**：更好的错误处理和跨平台兼容性
2. **Tmux替代Nohup**：更好的进程管理和日志查看
3. **简化配置**：移除--environment参数，只使用--config
4. **统一管理**：每个服务集群有自己的启动脚本

## 配置变更

- 移除了 `--environment` 参数
- 移除了 `--config-dir` 参数  
- 只保留 `--config` 参数用于指定配置文件路径

## Tmux会话管理

服务现在运行在tmux会话中，便于管理：

```bash
# 查看所有会话
tmux list-sessions

# 连接到服务会话查看日志
tmux attach-session -t orc-mongodb-services-orc
tmux attach-session -t orc-mongodb-services-mongodb

# 从会话中分离（不停止服务）
# 在tmux中按 Ctrl+B，然后按 D
```

## 详细文档

请查看各服务目录下的README.md文件获取详细使用说明：

- `services/orc_mongodb_service/README.md`

## 如需帮助

如果在迁移过程中遇到问题，请：

1. 查看对应服务的README.md文档
2. 使用状态检查脚本诊断问题
3. 连接到tmux会话查看实时日志
