#!/bin/bash
# -*- coding: utf-8 -*-
"""
停止所有微服务的脚本
"""

set -e

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
LOG_DIR="$PROJECT_ROOT/logs"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 停止单个服务
stop_service() {
    local service_name=$1
    local pid_file="$LOG_DIR/${service_name}.pid"
    
    if [[ -f "$pid_file" ]]; then
        local pid=$(cat "$pid_file")
        
        if kill -0 "$pid" 2>/dev/null; then
            log_info "停止 $service_name (PID: $pid)..."
            kill -TERM "$pid"
            
            # 等待进程结束
            local count=0
            while kill -0 "$pid" 2>/dev/null && [[ $count -lt 10 ]]; do
                sleep 1
                count=$((count + 1))
            done
            
            # 如果进程仍在运行，强制杀死
            if kill -0 "$pid" 2>/dev/null; then
                log_warn "强制停止 $service_name..."
                kill -KILL "$pid"
            fi
            
            log_info "$service_name 已停止"
        else
            log_warn "$service_name 进程不存在 (PID: $pid)"
        fi
        
        # 删除PID文件
        rm -f "$pid_file"
    else
        log_warn "$service_name PID文件不存在"
    fi
}

# 主函数
main() {
    log_info "=== 停止User-DF微服务集群 ==="
    
    # 停止所有服务
    stop_service "orc_processor_service"
    stop_service "mongodb_writer_service"
    stop_service "monitoring_service"
    
    # 清理其他可能的进程
    log_info "清理相关进程..."
    
    # 查找并停止相关Python进程
    pkill -f "services.orc_mongodb_service.orc_processor_service" || true
    pkill -f "services.orc_mongodb_service.mongodb_writer_service" || true
    pkill -f "services.orc_mongodb_service.monitoring_service" || true
    
    log_info "所有服务已停止"
}

# 执行主函数
main "$@"
