#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微服务架构演示脚本

演示如何使用新的微服务架构处理ORC文件
"""

import os
import sys
import asyncio
import json
import time
import requests
import redis
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from shared.core import ConfigManager, Logger


class MicroservicesDemo:
    """微服务架构演示"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.logger = Logger(__name__, self.config_manager)
        
        # 服务配置
        self.orc_processor_url = "http://localhost:8001"
        self.mongodb_writer_url = "http://localhost:8002"
        
        # Redis客户端
        self.redis_client = redis.Redis(
            host="localhost",
            port=6379,
            db=0,
            decode_responses=True
        )
    
    def check_services_health(self) -> bool:
        """检查所有服务的健康状态"""
        print("🔍 检查微服务健康状态...")
        
        services = [
            ("ORC处理服务", f"{self.orc_processor_url}/health"),
            ("MongoDB写入服务", f"{self.mongodb_writer_url}/health")
        ]
        
        all_healthy = True
        
        for service_name, health_url in services:
            try:
                response = requests.get(health_url, timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    status = data.get("status", "unknown")
                    uptime = data.get("uptime", 0)
                    
                    if status == "healthy":
                        print(f"  ✅ {service_name}: 健康 (运行时间: {uptime:.1f}秒)")
                    else:
                        print(f"  ❌ {service_name}: 状态异常 ({status})")
                        all_healthy = False
                else:
                    print(f"  ❌ {service_name}: HTTP错误 ({response.status_code})")
                    all_healthy = False
            except Exception as e:
                print(f"  ❌ {service_name}: 连接失败 ({e})")
                all_healthy = False
        
        return all_healthy
    
    def check_redis_connection(self) -> bool:
        """检查Redis连接"""
        print("🔍 检查Redis连接...")
        
        try:
            pong = self.redis_client.ping()
            if pong:
                print("  ✅ Redis: 连接正常")
                
                # 检查队列状态
                queue_name = "mongodb_write_queue"
                queue_length = self.redis_client.llen(queue_name)
                print(f"  📊 消息队列长度: {queue_length}")
                
                return True
            else:
                print("  ❌ Redis: ping失败")
                return False
        except Exception as e:
            print(f"  ❌ Redis: 连接失败 ({e})")
            return False
    
    def submit_processing_task(self, orc_file: str, process_date: str, prov_id: int) -> Optional[str]:
        """提交ORC文件处理任务"""
        print(f"📤 提交处理任务...")
        print(f"  文件: {orc_file}")
        print(f"  日期: {process_date}")
        print(f"  省份ID: {prov_id}")
        
        try:
            url = f"{self.orc_processor_url}/process"
            request_data = {
                "orc_file": orc_file,
                "process_date": process_date,
                "prov_id": prov_id
            }
            
            response = requests.post(url, json=request_data, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                task_id = data.get("task_id")
                
                if task_id:
                    print(f"  ✅ 任务已提交，任务ID: {task_id}")
                    return task_id
                else:
                    print(f"  ❌ 任务提交失败: 无任务ID")
                    return None
            else:
                print(f"  ❌ 任务提交失败: HTTP {response.status_code}")
                return None
                
        except Exception as e:
            print(f"  ❌ 任务提交失败: {e}")
            return None
    
    def monitor_task_progress(self, task_id: str, max_wait_time: int = 60) -> bool:
        """监控任务进度"""
        print(f"👀 监控任务进度: {task_id}")
        
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            try:
                # 检查任务状态
                task_url = f"{self.orc_processor_url}/task/{task_id}"
                response = requests.get(task_url, timeout=5)
                
                if response.status_code == 200:
                    task_data = response.json()
                    status = task_data.get("status", "unknown")
                    created_at = task_data.get("created_at", 0)
                    
                    elapsed = time.time() - created_at if created_at > 0 else 0
                    
                    print(f"  📊 任务状态: {status} (已运行: {elapsed:.1f}秒)")
                    
                    if status == "completed":
                        print("  ✅ 任务完成!")
                        return True
                    elif status == "failed":
                        print("  ❌ 任务失败!")
                        return False
                    elif status in ["pending", "processing"]:
                        print("  ⏳ 任务处理中...")
                    else:
                        print(f"  ❓ 未知状态: {status}")
                
                else:
                    print(f"  ❌ 无法获取任务状态: HTTP {response.status_code}")
                
                time.sleep(2)  # 等待2秒后再次检查
                
            except Exception as e:
                print(f"  ❌ 监控任务失败: {e}")
                time.sleep(2)
        
        print(f"  ⏰ 监控超时 ({max_wait_time}秒)")
        return False
    
    def check_queue_status(self):
        """检查消息队列状态"""
        print("📊 检查消息队列状态...")
        
        try:
            # 检查Redis队列
            queue_name = "mongodb_write_queue"
            queue_length = self.redis_client.llen(queue_name)
            print(f"  📦 Redis队列长度: {queue_length}")
            
            # 检查MongoDB写入服务的队列状态
            queue_status_url = f"{self.mongodb_writer_url}/queue/status"
            response = requests.get(queue_status_url, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                print(f"  🔄 MongoDB写入服务队列状态:")
                print(f"    队列名称: {data.get('queue_name', 'unknown')}")
                print(f"    队列长度: {data.get('queue_length', 'unknown')}")
                print(f"    处理状态: {'运行中' if data.get('is_processing', False) else '空闲'}")
            else:
                print(f"  ❌ 无法获取MongoDB写入服务队列状态: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ 检查队列状态失败: {e}")
    
    def get_services_stats(self):
        """获取服务统计信息"""
        print("📈 获取服务统计信息...")
        
        services = [
            ("ORC处理服务", f"{self.orc_processor_url}/stats"),
            ("MongoDB写入服务", f"{self.mongodb_writer_url}/stats")
        ]
        
        for service_name, stats_url in services:
            try:
                response = requests.get(stats_url, timeout=5)
                
                if response.status_code == 200:
                    data = response.json()
                    stats = data.get("stats", {})
                    
                    print(f"  📊 {service_name}:")
                    print(f"    总任务数: {stats.get('total_tasks', 0)}")
                    print(f"    完成任务: {stats.get('completed_tasks', 0)}")
                    print(f"    失败任务: {stats.get('failed_tasks', 0)}")
                    print(f"    当前任务: {data.get('current_tasks', 0)}")
                    
                    if 'total_users_processed' in stats:
                        print(f"    处理用户数: {stats['total_users_processed']}")
                    if 'total_users_written' in stats:
                        print(f"    写入用户数: {stats['total_users_written']}")
                        
                else:
                    print(f"  ❌ 无法获取{service_name}统计信息: HTTP {response.status_code}")
                    
            except Exception as e:
                print(f"  ❌ 获取{service_name}统计信息失败: {e}")
    
    def run_demo(self):
        """运行演示"""
        print("🚀 User-DF 微服务架构演示")
        print("=" * 50)
        
        # 1. 检查服务健康状态
        if not self.check_services_health():
            print("❌ 服务健康检查失败，请确保所有微服务正在运行")
            return False
        
        print()
        
        # 2. 检查Redis连接
        if not self.check_redis_connection():
            print("❌ Redis连接失败，请确保Redis服务正在运行")
            return False
        
        print()
        
        # 3. 获取初始统计信息
        print("📊 初始服务状态:")
        self.get_services_stats()
        print()
        
        # 4. 提交演示任务
        demo_tasks = [
            {
                "orc_file": "/demo/path/demo_file_1.orc",
                "process_date": "20250722",
                "prov_id": 200
            },
            {
                "orc_file": "/demo/path/demo_file_2.orc", 
                "process_date": "20250722",
                "prov_id": 210
            }
        ]
        
        task_ids = []
        
        for i, task_config in enumerate(demo_tasks, 1):
            print(f"📤 提交演示任务 {i}/{len(demo_tasks)}:")
            task_id = self.submit_processing_task(**task_config)
            
            if task_id:
                task_ids.append(task_id)
            
            print()
            time.sleep(1)  # 间隔1秒
        
        if not task_ids:
            print("❌ 没有成功提交的任务")
            return False
        
        # 5. 监控任务进度
        print("👀 监控任务进度:")
        for i, task_id in enumerate(task_ids, 1):
            print(f"监控任务 {i}/{len(task_ids)}:")
            success = self.monitor_task_progress(task_id, max_wait_time=30)
            print()
        
        # 6. 检查队列状态
        self.check_queue_status()
        print()
        
        # 7. 获取最终统计信息
        print("📊 最终服务状态:")
        self.get_services_stats()
        print()
        
        print("✅ 演示完成!")
        print("=" * 50)
        
        return True


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="微服务架构演示")
    parser.add_argument("--check-only", action="store_true", help="仅检查服务状态")
    
    args = parser.parse_args()
    
    demo = MicroservicesDemo()
    
    try:
        if args.check_only:
            # 仅检查服务状态
            print("🔍 检查微服务状态...")
            services_ok = demo.check_services_health()
            redis_ok = demo.check_redis_connection()
            
            if services_ok and redis_ok:
                print("\n✅ 所有服务状态正常")
                demo.get_services_stats()
                sys.exit(0)
            else:
                print("\n❌ 部分服务状态异常")
                sys.exit(1)
        else:
            # 运行完整演示
            success = demo.run_demo()
            sys.exit(0 if success else 1)
            
    except KeyboardInterrupt:
        print("\n演示被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"演示运行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
